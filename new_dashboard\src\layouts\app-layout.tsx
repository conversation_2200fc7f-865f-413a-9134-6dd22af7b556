import { Navigate, Outlet } from 'react-router-dom';
import { useApiQuery } from '../hooks/react-query-hooks';
import authEndpoints from '../api/service/auth';
import { Keys, LocalStorageService } from '../utils/local-storage';
import useFetchIntegrationConnections from '../hooks/use-integration-connections';
import FlableLoader from '../components/flable-loader/flable-loader';
import onboardingEndpoints, {
   UserSocialDetails,
} from '../api/service/onboarding';
import { useAppDispatch, useAppSelector } from '../store/store';
import {
   setMasterList,
   setOptimisationsStatus,
} from '../store/reducer/onboarding-reducer';
import keys from '../utils/strings/query-keys';
import { setUserConnectionDetails } from '../store/reducer/user-details-reducer';
import { SettingsQueryKeys } from '../pages/dashboard/utils/query-keys';
import settingsBackendEndpoints from '../api/service/settings';
import { setGeneralSettings } from '../store/reducer/settings-reducer';
import { SidebarInset, SidebarProvider } from '@/components/ui/sidebar';
import { AppSidebar } from '@/components/sidenavbar/app-sidebar';
import AppHeader from '@/components/header/app-header';
import Subscription from '@/pages/settings/subscription/plan-topups/subscription';
import { useEffect } from 'react';
import { setNotificationPermission } from '@/store/reducer/analytics-agent-reducer';
import { toast } from 'sonner';

interface OptimisationStatusProps {
   complete: boolean;
   channels_marketplace: boolean;
   shipping_logistics: boolean;
   flable_pixel: boolean;
   ads_account: boolean;
   competitors: boolean;
   seo: boolean;
   socials: boolean;
}

export type OptimisationStatusKeys =
   | 'complete'
   | 'channels_marketplace'
   | 'shipping_logistics'
   | 'flable_pixel'
   | 'ads_account'
   | 'competitors'
   | 'seo'
   | 'socials';

const channelGroups = {
   ads_account: ['googleads', 'facebookads'],
   //added shopifyconnect will replace naming with shopify once app is live
   channels_marketplace: [
      'shopify',
      'shopifyconnect',
      'amazon_selling_partner',
   ],
   seo: ['gsc'],
};

const AppLayout = () => {
   const dispatch = useAppDispatch();

   const token = LocalStorageService.getItem(Keys.Token);
   const authUser = LocalStorageService.getItem(Keys.FlableUserDetails);
   const email_address = LocalStorageService.getItem(Keys.UserName) as string;
   const client_id = LocalStorageService.getItem(Keys.ClientId) as string;

   const { notificationPermission } = useAppSelector(
      (state) => state.analyticsAgent,
   );

   useApiQuery({
      queryKey: [keys.userMasterList],
      queryFn: () => onboardingEndpoints.fetchUserMasterList(),
      selectHandler: (data) => {
         dispatch(setMasterList(data.list));
         return data;
      },
      enabled: true,
      refetchOnWindowFocus: false,
      refetchOnReconnect: false,
      refetchOnMount: false,
   });

   useApiQuery({
      queryKey: [SettingsQueryKeys.generalSettings],
      queryFn: () =>
         settingsBackendEndpoints.fetchGeneralSettings({
            client_id,
            email_address,
         }),
      enabled: true,
      selectHandler: (data) => {
         dispatch(setGeneralSettings(data));
         return data;
      },
      refetchOnWindowFocus: false,
   });

   const {
      data: emailVerificationData,
      isLoading: isEmailVerificationLoading,
   } = useApiQuery({
      queryKey: ['isEmailVerified'],
      queryFn: () =>
         authEndpoints?.isEmailVerified({
            email_address: email_address,
         }),
      enabled: true,
      refetchOnReconnect: false,
      refetchOnWindowFocus: false,
   });

   const { isLoading: isSocialAnalyticsLoading } = useApiQuery({
      queryKey: ['userSocialDetails'],
      queryFn: () =>
         onboardingEndpoints.getUserSocialDetails({
            client_id: LocalStorageService.getItem(Keys.ClientId) as string,
         }),
      selectHandler: (data) => {
         if (data?.details?.length <= 0) {
            return data;
         }
         dispatch(setUserConnectionDetails(data.details));
         const optimisationsStatus: OptimisationStatusProps = {
            complete: false,
            channels_marketplace: false,
            shipping_logistics: false,
            flable_pixel: false,
            ads_account: false,
            competitors: false,
            seo: false,
            socials: false,
         };

         data.details.forEach((detail: UserSocialDetails) => {
            const { channel_name } = detail;

            if (channel_name === 'flable_pixel') {
               optimisationsStatus.flable_pixel = true;
            } else {
               for (const [key, channels] of Object.entries(channelGroups)) {
                  if (channels.includes(channel_name)) {
                     optimisationsStatus[key as OptimisationStatusKeys] = true;
                     break;
                  }
               }
            }
         });

         if (
            data?.details?.[0]?.competitor_handles &&
            data?.details?.[0]?.competitor_handles?.length > 0
         ) {
            optimisationsStatus.competitors = true;
         }

         optimisationsStatus.complete = Object.entries(optimisationsStatus)
            .filter(([key]) => key !== 'complete')
            .every(([, status]) => status);

         dispatch(setOptimisationsStatus(optimisationsStatus));
         return data;
      },
      enabled: true,
      refetchOnReconnect: false,
      refetchOnWindowFocus: false,
   });

   useFetchIntegrationConnections();

   Subscription();

   const requestPermission = () => {
      if (!('Notification' in window)) {
         alert('This browser does not support desktop notifications.');
         return;
      }

      Notification.requestPermission()
         .then((perm) => {
            dispatch(setNotificationPermission(perm));
            if (perm === 'granted') {
               new Notification('Notifications enabled!', {
                  body: 'You’ll now get updates from flable.ai.',
               });
            }
         })
         .catch((error) => {
            console.error('Notification permission request failed:', error);
         });
   };

   useEffect(() => {
      dispatch(setNotificationPermission(Notification.permission));

      if (
         notificationPermission === 'default' &&
         !isEmailVerificationLoading &&
         !isSocialAnalyticsLoading
      ) {
         toast.info('Enable Notifications', {
            description: 'Flable.ai would like to send you notifications.',
            action: {
               label: 'Enable',
               onClick: requestPermission,
            },
         });
      }
   }, []);

   if (isEmailVerificationLoading || isSocialAnalyticsLoading) {
      return <FlableLoader />;
   }

   if (!token || !authUser || !emailVerificationData?.is_email_verified) {
      return <Navigate replace to='/auth/login' />;
   }

   return (
      <SidebarProvider>
         <AppSidebar />
         <SidebarInset className='h-[100vh] max-h-[100vh] overflow-hidden'>
            <AppHeader />
            <div className='h-[calc(100vh-52px)] overflow-y-auto'>
               <Outlet />
            </div>
         </SidebarInset>
      </SidebarProvider>
   );
};

export default AppLayout;
