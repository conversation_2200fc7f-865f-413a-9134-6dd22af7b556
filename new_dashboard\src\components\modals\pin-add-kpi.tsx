import {
   Collapse,
   Flex,
   Heading,
   Text,
   useToast,
   VStack,
} from '@chakra-ui/react';
import {
   DragDropContext,
   Droppable,
   Draggable,
   DropResult,
} from 'react-beautiful-dnd';
import { MdOutlineDragIndicator } from 'react-icons/md';
import ModalWrapper from './modal-wrapper';
import './pin-add-kpi.scss';
import { useAppSelector } from '../../store/store';
import { FaPlus, FaMinus, FaChevronUp, FaChevronDown } from 'react-icons/fa';
import { useApiMutation } from '../../hooks/react-query-hooks';
import kpiService from '../../api/service/kpi/index';
import KPIQueryKeys from '../../pages/dashboard/utils/query-keys';
import { Keys, LocalStorageService } from '../../utils/local-storage';
import { useState } from 'react';
import { KPICategory } from '../../utils/strings/kpi-constants';
import { useDispatch } from 'react-redux';
import {
   setUpdatedPins,
   togglePinnedVisible,
   toggleVisible,
} from '../../store/reducer/kpi-reducer';
import {
   KpiCategoryKeys,
   KPIMeta,
   PinPayload,
   PinVisibleOrderPayload,
   VisblePayload,
} from '@/pages/dashboard/utils/interface';
import {
   getAddedAvailable,
   getAggData,
   reorder,
} from '@/pages/dashboard/utils/helpers';
function AddKPIModal() {
   const [collapseCat, setcollapseCat] = useState<{
      [key: string]: boolean;
   }>({});
   const currentModal = useAppSelector((state) => state.modal);
   const { head, metaData } = currentModal.payload?.modalProps as {
      head: string;
      metaData: KPIMeta[];
   };
   const toast = useToast();
   const dispatch = useDispatch();
   const handleMetaSuccess = (
      data: string,
      payload?: VisblePayload | PinPayload,
   ) => {
      dispatch(
         setUpdatedPins(
            `${payload?.category || data} - ${payload?.kpis[0] || data}` ||
               data,
         ),
      );
   };
   const hanldleOrderSuccess = (
      data: string,
      payload?: PinVisibleOrderPayload,
   ) => {
      dispatch(
         setUpdatedPins(
            `${payload?.pinOrder || data} - ${payload?.kpiOrder.map((x) => x.kpi).join(', ') || data}` ||
               data,
         ),
      );
   };
   const { mutate: updatePinned, errorMessage: pinnedErr } = useApiMutation({
      queryKey: [KPIQueryKeys.kpiPinned],
      mutationFn: kpiService.updatePinned,
      onSuccessHandler: handleMetaSuccess,
   });
   const { mutate: updateVisible, errorMessage: VisibleErr } = useApiMutation({
      queryKey: [KPIQueryKeys.kpiVisible],
      mutationFn: kpiService.updateVisible,
      onSuccessHandler: handleMetaSuccess,
   });
   const { mutate: updatePinVisibleOrder, errorMessage: pinVisibleErr } =
      useApiMutation({
         queryKey: [KPIQueryKeys.pinVisibleOrder],
         mutationFn: kpiService.updatePinVisibleOrder,
         onSuccessHandler: hanldleOrderSuccess,
      });

   const connectorKpiData = useAppSelector((state) => state.kpi.connectorData);

   const { added, available } = getAddedAvailable(
      head,
      metaData,
      connectorKpiData,
   );

   const [included, setIncluded] = useState(added);
   const [notIncluded, setnotIncluded] = useState(available);
   const handleAdd = (
      kpi: KPIMeta,
      newIncluded?: KPIMeta[],
      newNotIncluded?: KPIMeta[],
   ) => {
      if (head == 'pinned') {
         updatePinned({
            clientId: LocalStorageService.getItem(Keys.ClientId) || '',
            category: kpi.category,
            kpis: [kpi.kpi],
            pinned: true,
         });
         // Add KPI
         dispatch(
            togglePinnedVisible({
               category: kpi.category,
               kpi: kpi.kpi,
               add: true,
            }),
         );
      } else {
         updateVisible({
            clientId: LocalStorageService.getItem(Keys.ClientId) || '',
            category: kpi.category,
            kpis: [kpi.kpi],
            visible: true,
         });
         dispatch(toggleVisible(kpi.kpi)); // 🔹 update meta visibility
      }

      if (newIncluded) setIncluded(newIncluded);
      else {
         setIncluded((prev) => {
            return [...prev, kpi];
         });
      }
      if (newNotIncluded) setnotIncluded(newNotIncluded);
      else {
         setnotIncluded((prev) => {
            return prev.filter(
               (k) => `${k.category}${k.kpi}` !== `${kpi.category}${kpi.kpi}`,
            );
         });
      }
   };

   const onDragEnd = (result: DropResult) => {
      const { source, destination } = result;
      // dropped outside the list
      if (!destination) {
         return;
      }
      let finalOrderedItems: KPIMeta[] = [];
      if (source.droppableId === destination.droppableId) {
         finalOrderedItems = reorder(
            source.droppableId === 'included'
               ? [...included]
               : [...notIncluded],
            source.index,
            destination.index,
         );
         if (source.droppableId === 'included') {
            setIncluded(finalOrderedItems);
         } else {
            setnotIncluded(finalOrderedItems);
         }
      } else {
         const sourceItems =
            source.droppableId === 'included'
               ? [...included]
               : [...notIncluded];
         finalOrderedItems =
            source.droppableId !== 'included'
               ? [...included]
               : [...notIncluded];
         const [removed] = sourceItems.splice(source.index, 1);
         finalOrderedItems.splice(destination.index, 0, removed);
         if (source.droppableId === 'included') {
            handleRemove(removed, sourceItems, finalOrderedItems);
         } else {
            handleAdd(removed, finalOrderedItems, sourceItems);
         }
      }
      updatePinVisibleOrder({
         clientId: LocalStorageService.getItem(Keys.ClientId) || '',
         kpiOrder: finalOrderedItems.map((x, idx) => {
            return {
               kpi: x.kpi,
               order: idx + 1,
            };
         }),
         pinOrder: head == 'pinned',
      });
   };

   const handleRemove = (
      kpi: KPIMeta,
      newIncluded?: KPIMeta[],
      newNotIncluded?: KPIMeta[],
   ) => {
      if (head == 'pinned') {
         updatePinned({
            clientId: LocalStorageService.getItem(Keys.ClientId) || '',
            category: kpi.category,
            kpis: [kpi.kpi],
            pinned: false,
         });

         // Remove KPI
         dispatch(
            togglePinnedVisible({
               category: kpi.category,
               kpi: kpi.kpi,
               add: false,
            }),
         );
      } else {
         updateVisible({
            clientId: LocalStorageService.getItem(Keys.ClientId) || '',
            category: kpi.category,
            kpis: [kpi.kpi],
            visible: false,
         });
         dispatch(toggleVisible(kpi.kpi)); // 🔹 update meta visibility
      }

      if (newIncluded) setIncluded(newIncluded);
      else {
         setIncluded((prev) => {
            return prev.filter(
               (k) => `${k.category}${k.kpi}` !== `${kpi.category}${kpi.kpi}`,
            );
         });
      }
      if (newNotIncluded) setnotIncluded(newNotIncluded);
      else {
         setnotIncluded((prev) => {
            return [...prev, kpi];
         });
      }
   };

   const key: KpiCategoryKeys = head.trim() as KpiCategoryKeys;
   if (pinnedErr || VisibleErr || pinVisibleErr) {
      toast({
         title: 'Error updating added KPI"s',
         description: pinnedErr || VisibleErr || pinVisibleErr,
         status: 'error',
         duration: 5000,
         isClosable: true,
      });
   }

   return (
      <ModalWrapper
         heading={KPICategory[key] || head}
         parentClassName='add-kpi'
      >
         <Heading as='h2' fontSize={'20px'} fontWeight={600} marginBottom={2}>
            Included
         </Heading>
         <DragDropContext onDragEnd={onDragEnd}>
            <Droppable droppableId='included'>
               {(provided) => (
                  <VStack
                     alignItems={'left'}
                     marginBottom={2}
                     gap={0}
                     ref={provided.innerRef}
                     {...provided.droppableProps}
                  >
                     {included.map((kpi, index) => (
                        <Draggable
                           key={kpi.category + kpi.kpi}
                           draggableId={kpi.category + kpi.kpi}
                           index={index}
                        >
                           {(provided, snapshot) => (
                              <Flex
                                 key={kpi.category + kpi.kpi + index}
                                 className='kpi'
                                 background={
                                    snapshot.isDragging ? '#bbddff' : ''
                                 }
                                 ref={provided.innerRef}
                                 {...provided.draggableProps}
                                 {...provided.dragHandleProps}
                              >
                                 <Flex alignItems={'center'} gap={1}>
                                    <MdOutlineDragIndicator className='drag-indicator' />
                                    <Text
                                       fontSize={'13px'}
                                       fontWeight={'600'}
                                       paddingTop={1}
                                    >
                                       {head === 'pinned'
                                          ? `${KPICategory[kpi.category] || kpi.category} - `
                                          : ''}
                                       {kpi.kpi_display_name}
                                    </Text>
                                 </Flex>
                                 <FaMinus
                                    color='red'
                                    fontWeight={400}
                                    cursor={'pointer'}
                                    onClick={() => handleRemove(kpi)}
                                 />
                              </Flex>
                           )}
                        </Draggable>
                     ))}
                     {provided.placeholder}
                  </VStack>
               )}
            </Droppable>
            <Heading
               as='h2'
               fontSize={'20px'}
               fontWeight={600}
               marginBottom={3}
               marginTop={3}
            >
               More KPI's
            </Heading>

            {head !== 'pinned' ? (
               <Droppable droppableId='not-included'>
                  {(provided) => (
                     <VStack
                        alignItems={'left'}
                        marginBottom={2}
                        ref={provided.innerRef}
                        {...provided.droppableProps}
                        gap={0}
                     >
                        {notIncluded.map((kpi, index) => (
                           <Draggable
                              key={kpi.category + kpi.kpi}
                              draggableId={kpi.category + kpi.kpi}
                              index={index}
                           >
                              {(provided, snapshot) => (
                                 <Flex
                                    key={kpi.category + kpi.kpi + index}
                                    className='kpi'
                                    background={
                                       snapshot.isDragging ? '#bbddff' : ''
                                    }
                                    ref={provided.innerRef}
                                    {...provided.draggableProps}
                                    {...provided.dragHandleProps}
                                 >
                                    <Flex alignItems={'center'} gap={1}>
                                       <MdOutlineDragIndicator className='drag-indicator' />
                                       <Text
                                          fontSize={'13px'}
                                          fontWeight={'600'}
                                          paddingTop={1}
                                       >
                                          {head === 'pinned'
                                             ? `${KPICategory[kpi.category] || kpi.category} - `
                                             : ''}
                                          {kpi.kpi_display_name}
                                       </Text>
                                    </Flex>
                                    <FaPlus
                                       color='green'
                                       fontWeight={400}
                                       cursor={'pointer'}
                                       onClick={() => handleAdd(kpi)}
                                    />
                                 </Flex>
                              )}
                           </Draggable>
                        ))}
                        {provided.placeholder}
                     </VStack>
                  )}
               </Droppable>
            ) : (
               <VStack alignItems={'left'} marginBottom={2} gap={2}>
                  {Object.entries(getAggData(notIncluded))
                     .sort()
                     .map(([category, kpis], idx) => {
                        return (
                           <Flex key={idx} direction={'column'}>
                              <Heading
                                 display={'flex'}
                                 gap={3}
                                 ml={3}
                                 alignItems={'center'}
                                 fontSize={'16px'}
                                 fontWeight={'500'}
                                 onClick={() =>
                                    setcollapseCat((prev) => {
                                       return {
                                          ...prev,
                                          [category]: !collapseCat[category],
                                       };
                                    })
                                 }
                              >
                                 {KPICategory[category] || category}{' '}
                                 {collapseCat[category] ? (
                                    <FaChevronUp className='cursor-pointer' />
                                 ) : (
                                    <FaChevronDown className='cursor-pointer' />
                                 )}
                              </Heading>
                              <Collapse in={!!collapseCat[category]}>
                                 <Flex
                                    justifyContent={'space-between'}
                                    direction={'column'}
                                    pt={4}
                                    pl={4}
                                 >
                                    {kpis
                                       .sort(
                                          (a, b) =>
                                             a.visible_order - b.visible_order,
                                       )
                                       .map((kpi) => (
                                          <Flex
                                             key={kpi.category + kpi.kpi}
                                             className='kpi'
                                             pl={4}
                                             pr={4}
                                          >
                                             <Flex
                                                alignItems={'center'}
                                                gap={1}
                                             >
                                                <Text
                                                   fontSize={'13px'}
                                                   fontWeight={'600'}
                                                   paddingTop={1}
                                                >
                                                   {kpi.kpi_display_name}
                                                </Text>
                                             </Flex>
                                             <FaPlus
                                                color='green'
                                                fontWeight={400}
                                                cursor={'pointer'}
                                                onClick={() => handleAdd(kpi)}
                                             />
                                          </Flex>
                                       ))}
                                 </Flex>
                              </Collapse>
                           </Flex>
                        );
                     })}
               </VStack>
            )}
         </DragDropContext>
      </ModalWrapper>
   );
}

export default AddKPIModal;
