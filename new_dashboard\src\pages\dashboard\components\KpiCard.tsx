import { useApiMutation } from '@/hooks/react-query-hooks';
import K<PERSON>QueryKeys from '@/pages/dashboard/utils/query-keys';
import {
   setUpdatedPins,
   togglePinned,
   togglePinnedKPIs,
   setAlertsAndOpportunities,
   removeAlertOrOpportunity,
} from '@/store/reducer/kpi-reducer';
import {
   Flex,
   Text,
   Tooltip,
   useColorMode,
   useToast,
   VStack,
   WrapItem,
} from '@chakra-ui/react';
import { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import kpiService from '../../../api/service/kpi/index';
import { Keys, LocalStorageService } from '@/utils/local-storage';
import { calculateHelper } from '@/pages/utils/kpiCalculaterHelper';
import { useState } from 'react';
import { GrCircleQuestion } from 'react-icons/gr';
import KPIImage from '@/pages/dashboard/components/kpi-image';
import <PERSON><PERSON>hart from './LineChart';
import { Bs<PERSON>inAngle, BsPinAngleFill } from 'react-icons/bs';
import TooltipContent from '@/pages/dashboard/components/tooltip-content';
import { KpiDataWithMeta, PinPayload, VisblePayload } from '../utils/interface';
import { IoIosWarning } from 'react-icons/io';
import { MdCheckCircle } from 'react-icons/md';
import { Value } from './Value';
import ViewDetails from './ViewDetails';

function KPICard(props: {
   key: string;
   keyValue: string;
   kpiDetails: KpiDataWithMeta;
   anomaly: boolean | null;
}) {
   const { keyValue, kpiDetails, anomaly } = props;
   console.log('kpiDetails===>', kpiDetails);
   const [showRightIcons, setShowRightIcons] = useState(false);
   const toast = useToast();
   const dispatch = useDispatch();
   const { colorMode } = useColorMode();
   const handleMetaSuccess = (
      data: string,
      payload?: VisblePayload | PinPayload,
   ) => {
      dispatch(
         setUpdatedPins(
            `${payload?.category || data} - ${payload?.kpis[0] || data}` ||
               data,
         ),
      );
   };

   const { mutate: updatePinned, errorMessage: pinnedErr } = useApiMutation({
      queryKey: [KPIQueryKeys.kpiPinned],
      mutationFn: kpiService.updatePinned,
      onSuccessHandler: handleMetaSuccess,
   });
   const handleAdd = (pinStatus: boolean) => {
      // const kpi = kpiD;
      updatePinned({
         clientId: LocalStorageService.getItem(Keys.ClientId) || '',
         category: kpiDetails.category,
         kpis: [kpiDetails.kpi_names],
         pinned: !pinStatus,
      });
      dispatch(togglePinned(kpiDetails.kpi_names));
      dispatch(togglePinnedKPIs(kpiDetails));
      dispatch(setUpdatedPins(kpiDetails.kpi_names + kpiDetails.category));
   };
   if (pinnedErr) {
      toast({
         title: 'Error updating added KPI"s',
         description: pinnedErr,
         status: 'error',
         duration: 5000,
         isClosable: true,
      });
   }
   const imageStyles: object = {
      height: kpiDetails.category == 'amazon_ads' ? '16px' : '20px',
      width: kpiDetails.category == 'amazon_ads' ? '25px' : '20px',
      position: kpiDetails.category == 'amazon_ads' ? 'relative' : '',
      top: kpiDetails.category == 'amazon_ads' ? '3px' : '',
   };

   // const value = anomaly;
   const { percentage, color, direction } = calculateHelper(
      kpiDetails.kpi_names,
      kpiDetails.current_total_value,
      kpiDetails.previous_total_value,
   );
   useEffect(() => {
      if (
         percentage &&
         direction &&
         anomaly !== null &&
         anomaly !== undefined
      ) {
         dispatch(
            setAlertsAndOpportunities({
               kpiName: kpiDetails.kpi_display_name,
               direction: direction === 'is up' ? 'up' : 'down',
               isPositive: anomaly === true,
               percentage,
               message: `${kpiDetails.kpi_display_name} is ${direction === 'is up' ? 'up' : 'down'} by ${percentage}%`,
               category: kpiDetails.category,
               remove: false,
            }),
         );
      } else {
         dispatch(removeAlertOrOpportunity(kpiDetails.kpi_display_name));
      }
   }, [percentage, direction, anomaly]);
   const arrow = direction === 'is up' ? '↑' : '↓';

   return (
      <WrapItem
         minWidth={100}
         background={
            anomaly === true
               ? '#36B37E1A'
               : anomaly === false
                 ? '#FF56301A'
                 : colorMode === 'dark'
                   ? 'gray.800'
                   : 'white'
         }
         boxShadow={
            colorMode === 'dark'
               ? '1px 1px 10px 1px #00000033'
               : '1px 1px 10px 1px #cccccc33'
         }
         padding={5}
         borderRadius={5}
         className='kpi-item'
         position={'relative'}
         onMouseOver={() => setShowRightIcons(true)}
         onMouseLeave={() => setShowRightIcons(false)}
      >
         <VStack width='100%' alignItems={'flex-start'} flexGrow={1}>
            <div className='anomaly'>
               {anomaly === true && <MdCheckCircle size={26} color='#36B37E' />}
               {anomaly === false && <IoIosWarning size={26} color='#FF5630' />}
            </div>
            {showRightIcons && (
               <>
                  <div className='right-items'>
                     <TooltipContent
                        placement='top'
                        hasArrow
                        kpi={kpiDetails.kpi_names}
                     >
                        <GrCircleQuestion />
                     </TooltipContent>
                     <Tooltip
                        placement='top'
                        hasArrow
                        label={kpiDetails.pinned ? 'Unpin KPI' : 'Pin KPI'}
                     >
                        <div
                           className='pin-item'
                           onClick={() => handleAdd(!!kpiDetails.pinned)}
                        >
                           {getPinIcon(kpiDetails.pinned)}
                        </div>
                     </Tooltip>
                  </div>
               </>
            )}

            <Flex width={'100%'}>
               <Flex
                  direction={'column'}
                  alignItems={'flex-start'}
                  textAlign={'left'}
                  maxWidth={'40%'}
               >
                  <Value
                     totalVal={kpiDetails.current_total_value}
                     kpi_unit={kpiDetails.kpi_unit}
                     kpi_names={kpiDetails.kpi_names}
                     kpi_type={kpiDetails.kpi_type}
                  />

                  <Text
                     fontSize={11}
                     marginY={3}
                     fontWeight={600}
                     color={colorMode === 'dark' ? 'gray.400' : 'gray'}
                     maxWidth={'6rem'}
                     display={'flex'}
                     alignItems={'center'}
                     gap={2}
                  >
                     {keyValue == 'pinned' && (
                        <KPIImage
                           style={imageStyles}
                           kpiCat={kpiDetails.category}
                        />
                     )}
                     {kpiDetails.kpi_display_name}
                  </Text>
                  <h6 style={{ color }}>
                     <b>{percentage && `${percentage}% ${arrow}`}</b>
                  </h6>
               </Flex>
               <LineChart
                  kpiDetails={kpiDetails}
                  value={kpiDetails.current_allData}
                  anomaly={anomaly}
               />
            </Flex>
            <ViewDetails kpiDetails={kpiDetails} anomaly={anomaly} />
         </VStack>
      </WrapItem>
   );
}

export default KPICard;

const getPinIcon = (pinned: boolean | undefined) => {
   return pinned ? <BsPinAngleFill /> : <BsPinAngle />;
};
