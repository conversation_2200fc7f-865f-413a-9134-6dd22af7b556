import { Button } from '@/components/ui/button';

export interface AgentCardProps {
   image: string;
   heading: string;
   description: string;
   primaryButtonText: string;
   primaryButtonAction?: () => void;
   primaryButtonDisabled?: boolean;
   renderPrimaryButton?: () => JSX.Element;
   secondaryButtonText?: string;
   secondaryButtonAction?: () => void;
   secondaryButtonDisabled?: boolean;
   renderSecondaryButton?: () => JSX.Element;
   toggleSwitch?: (value: boolean) => void;
   toggle?: boolean;
}

const AgentCard = (props: AgentCardProps) => {
   const {
      image,
      heading,
      description,
      primaryButtonText,
      primaryButtonAction,
      primaryButtonDisabled,
      renderPrimaryButton,
      secondaryButtonText,
      secondaryButtonAction,
      secondaryButtonDisabled,
      renderSecondaryButton,
   } = props;

   return (
      <>
         <div className='agent-card flex flex-col sm:flex-row shadow-md rounded-md'>
            <img
               src={image}
               alt={heading}
               className='min-w-[200px] h-auto sm:min-w-[180px] sm:max-h-[250px] rounded-l-md'
            />
            <div className='flex flex-col h-full justify-between gap-4 p-4'>
               <div>
                  <h1 className='text-lg !text-black font-bold'>{heading}</h1>
                  <p className='text-sm mt-2'>{description}</p>
               </div>
               <div className='flex justify-start sm:justify-end gap-2'>
                  {!secondaryButtonText && renderSecondaryButton
                     ? renderSecondaryButton()
                     : secondaryButtonText && (
                          <Button
                             variant='outline'
                             size='lg'
                             onClick={secondaryButtonAction}
                             disabled={secondaryButtonDisabled || false}
                          >
                             {secondaryButtonText}
                          </Button>
                       )}
                  {!primaryButtonText && renderPrimaryButton ? (
                     renderPrimaryButton()
                  ) : (
                     <Button
                        variant='default'
                        size='lg'
                        onClick={primaryButtonAction}
                        disabled={primaryButtonDisabled || false}
                     >
                        {primaryButtonText}
                     </Button>
                  )}
               </div>
            </div>
         </div>
      </>
   );
};

export default AgentCard;
