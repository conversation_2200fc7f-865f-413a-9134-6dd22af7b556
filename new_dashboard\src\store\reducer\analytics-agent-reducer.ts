import { PayloadAction, createSlice } from '@reduxjs/toolkit';

interface KpiMetadata {
   kpi: string;
   currency: string;
   currency_threshold: string;
   start_date: string;
   end_date: string;
   prev_start_date: string;
   prev_end_date: string;
   percentage_change: string;
   source: 'root-cause' | 'optimize' | 'scale';
   platform:
      | 'META_KPI_PROMPTS'
      | 'GOOGLE_ADS_KPI_PROMPTS'
      | 'META_PULSE_OPTIMIZE_PROMPTS'
      | 'META_PULSE_SCALING_PROMPTS';
   campaign_id?: string;
   campaign_name?: string;
}

export interface Step {
   step: string;
   status: string;
   timestamp: string;
   sub_content: string;
   sub_content_type: 'text' | 'sql';
}

export interface ChatProgress {
   current_status: string;
   steps: Step[];
   user_question: string;
   last_updated: string;
}

interface InitialState {
   currentMode: 'data-analyst' | 'cmo';
   currentSessionID: string;
   currentPage: number;
   chatProgress: Record<string, ChatProgress>;
   notificationPermission: NotificationPermission;
   runningBookmarks: string[];
   runningChats: string[];
   kpiDisplayPrompt: string;
   kpiAiPrompt: string;
   kpiMetadata: KpiMetadata | null;
}

const analyticsAgentState: InitialState = {
   currentSessionID: '',
   currentMode: 'data-analyst',
   currentPage: 1,
   chatProgress: {},
   notificationPermission: 'default',
   runningBookmarks: [],
   runningChats: [],
   kpiDisplayPrompt: '',
   kpiAiPrompt: '',
   kpiMetadata: null,
};

const analyticsAgentSlice = createSlice({
   name: 'analyticsAgent',
   initialState: analyticsAgentState,
   reducers: {
      setCurrentSessionID: (state, action: PayloadAction<string>) => {
         state.currentSessionID = action.payload;
      },
      setCurrentMode: (
         state,
         action: PayloadAction<'data-analyst' | 'cmo'>,
      ) => {
         state.currentMode = action.payload;
      },
      setCurrentPage: (state, action: PayloadAction<number>) => {
         state.currentPage = action.payload;
      },
      setChatProgress: (
         state,
         action: PayloadAction<Record<string, ChatProgress>>,
      ) => {
         state.chatProgress = {
            ...state.chatProgress,
            ...action.payload,
         };
      },
      setNotificationPermission: (
         state,
         action: PayloadAction<NotificationPermission>,
      ) => {
         state.notificationPermission = action.payload;
      },
      setKpiPrompts: (
         state,
         action: PayloadAction<{ displayPrompt: string; aiPrompt: string }>,
      ) => {
         state.kpiDisplayPrompt = action.payload.displayPrompt;
         state.kpiAiPrompt = action.payload.aiPrompt;
      },
      clearKpiPrompts: (state) => {
         state.kpiDisplayPrompt = '';
         state.kpiAiPrompt = '';
      },
      setRunningBookmarks: (state, action: PayloadAction<string[]>) => {
         state.runningBookmarks = action.payload;
      },
      setRunningChats: (state, action: PayloadAction<string[]>) => {
         state.runningChats = action.payload;
      },
      setKpiMetadata: (state, action: PayloadAction<KpiMetadata | null>) => {
         state.kpiMetadata = action.payload;
      },
      clearKpiMetadata: (state) => {
         state.kpiMetadata = null;
      },
   },
});

export const {
   setCurrentSessionID,
   setCurrentMode,
   setCurrentPage,
   setChatProgress,
   setNotificationPermission,
   setKpiPrompts,
   clearKpiPrompts,
   setRunningBookmarks,
   setRunningChats,
   setKpiMetadata,
   clearKpiMetadata,
} = analyticsAgentSlice.actions;

export default analyticsAgentSlice.reducer;
