import { noZeroKPI } from '@/utils/strings/kpi-constants';
import { useColorMode } from '@chakra-ui/react';
import { useEffect, useState } from 'react';
import Chart from 'react-apexcharts';
import { ChartProp, KpiTimelineItem } from '../utils/interface';
import { ApexOptions } from 'apexcharts';
import { getChartDateLabel, getFormattedVal, toHHMMSS } from '../utils/helpers';

function LineChart(props: ChartProp) {
   const { kpiDetails, value, anomaly } = props;
   const categories = getChartDateLabel(value as KpiTimelineItem[]);
   const colorMode = useColorMode();
   const chartColor =
      anomaly === true ? '#0E9F6E' : anomaly === false ? '#FF5630' : '';

   const [chartData, setchartData] = useState({
      options: {
         chart: {
            id: kpiDetails.kpi_display_name,
            toolbar: {
               show: false,
            },
            zoom: {
               enabled: false,
            },
         },
         tooltip: {
            y: {
               formatter: function (value: number) {
                  if (!value && noZeroKPI.includes(kpiDetails.kpi_names))
                     return 'N/A';
                  return kpiDetails.kpi_unit == 'time'
                     ? toHHMMSS(value)
                     : getFormattedVal(Math.round(value * 100) / 100);
               },
            },
         },
         scales: {
            y: {
               display: false,
            },
         },
         xaxis: {
            //type: groupBy == 'day' ? 'datetime' : 'string',
            categories: categories,
            labels: {
               show: false,
               style: {
                  colors: colorMode ? '#FFFFFF' : '#000000',
               },
            },
         },
         yaxis: {
            labels: {
               show: false,
               style: {
                  colors: colorMode ? '#FFFFFF' : '#000000',
               },
            },
         },
         stroke: {
            curve: 'smooth',
         },
         dataLabels: {
            enabled: false,
         },
         grid: {
            show: false,
         },
         colors: chartColor ? [chartColor] : undefined,
      },
      series: [
         {
            name: kpiDetails.kpi_display_name,
            data: (value as KpiTimelineItem[]).map((x) =>
               Number(x.kpi_value?.toFixed(2)),
            ),
         },
      ],
   });
   useEffect(() => {
      setchartData({
         options: {
            chart: {
               id: kpiDetails.kpi_display_name,
               toolbar: {
                  show: false,
               },
               zoom: {
                  enabled: false,
               },
            },
            scales: {
               y: {
                  display: false,
               },
            },
            tooltip: {
               y: {
                  formatter: function (value: number) {
                     if (!value && noZeroKPI.includes(kpiDetails.kpi_names))
                        return 'N/A';
                     return kpiDetails.kpi_unit == 'time'
                        ? toHHMMSS(value)
                        : getFormattedVal(Math.round(value * 100) / 100);
                  },
               },
            },
            xaxis: {
               //type: groupBy == 'day' ? 'datetime' : 'string',
               categories: categories,
               labels: {
                  show: false,
                  style: {
                     colors: colorMode ? '#FFFFFF' : '#000000',
                  },
               },
            },
            yaxis: {
               labels: {
                  show: false,
                  style: {
                     colors: colorMode ? '#FFFFFF' : '#000000',
                  },
               },
            },
            stroke: {
               curve: 'smooth',
            },
            dataLabels: {
               enabled: false,
            },
            grid: {
               show: false,
            },
            colors: chartColor ? [chartColor] : undefined,
         },
         series: [
            {
               name: kpiDetails.kpi_display_name,
               data: (value as KpiTimelineItem[]).map((x) =>
                  Number(x.kpi_value?.toFixed(2)),
               ),
            },
         ],
      });
   }, [value, colorMode]);

   return (
      <>
         <div
            style={{
               paddingTop: 25,
               flexGrow: 1,
               color: 'black',
               width: 0,
            }}
         >
            <Chart
               options={chartData.options as ApexOptions}
               series={chartData.series}
               type='area'
               height='120'
               width='100%'
            />
         </div>
      </>
   );
}

export default LineChart;
