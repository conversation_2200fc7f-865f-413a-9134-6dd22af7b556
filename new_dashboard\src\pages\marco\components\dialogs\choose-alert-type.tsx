import {
   AlertDialog,
   AlertDialogCancel,
   AlertDialogAction,
   AlertDialogContent,
   AlertDialogDescription,
   AlertDialogFooter,
   AlertDialogHeader,
   AlertDialogTitle,
   AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { resetForm } from '@/store/reducer/custom-alerts-reducer';
// import { setCurrentAgent } from '@/store/reducer/marco-reducer';
import { useAppDispatch } from '@/store/store';
import { useNavigate } from 'react-router-dom';

const ChooseAlertType = () => {
   const dispatch = useAppDispatch();
   const navigate = useNavigate();

   return (
      <AlertDialog>
         <AlertDialogTrigger asChild>
            <Button variant='default' size='lg'>
               Create Alert
            </Button>
         </AlertDialogTrigger>
         <AlertDialogContent className='bg-white dark:bg-gray-800'>
            <AlertDialogHeader>
               <AlertDialogTitle className='font-bold text-xl'>
                  Alert Type Selection
               </AlertDialogTitle>
               <AlertDialogDescription>
                  Choose how you want to create your alert. The agent can
                  understand your intent and create the alert for you, or you
                  can use a template to create it.
               </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
               <AlertDialogCancel className='font-semibold !text-sm'>
                  Cancel
               </AlertDialogCancel>
               <AlertDialogAction
                  className='font-normal !text-sm'
                  onClick={() => {
                     dispatch(resetForm());
                     navigate('/marco/custom-alerts/create-alert');
                  }}
               >
                  Using Template
               </AlertDialogAction>
               <AlertDialogAction
                  className='font-normal !text-sm'
                  onClick={() => {
                     navigate('/marco/alerting-agent');
                  }}
               >
                  Using Agent
               </AlertDialogAction>
            </AlertDialogFooter>
         </AlertDialogContent>
      </AlertDialog>
   );
};

export default ChooseAlertType;
