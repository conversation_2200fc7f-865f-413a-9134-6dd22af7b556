import {
   endEdornment,
   noZeroKPI,
   startEdornment,
} from '@/utils/strings/kpi-constants';
import { Text } from '@chakra-ui/react';
import { getFormattedVal, toHHMMSS } from '../utils/helpers';

export function Value(props: {
   totalVal: number;
   kpi_unit: string;
   kpi_names: string;
   kpi_type: string;
   fontSize?: number;
   rightAlign?: boolean;
}) {
   const { totalVal, kpi_unit, kpi_names, kpi_type, fontSize, rightAlign } =
      props;
   let value =
      kpi_unit == 'time'
         ? toHHMMSS(totalVal)
         : getFormattedVal(Math.round(totalVal * 100) / 100);

   if (!totalVal && noZeroKPI.includes(kpi_names)) value = 'N/A';
   const showEndornment = value !== 'N/A';
   return (
      <Text
         fontSize={fontSize || 24}
         textAlign={rightAlign ? 'right' : 'left'}
         fontWeight={700}
         display={'flex'}
         justifyContent={rightAlign ? 'flex-end' : 'flex-start'}
         gap={1}
      >
         {showEndornment && (startEdornment[kpi_type] || '')}
         <span>{value}</span>
         {showEndornment && (endEdornment[kpi_type] || '')}
      </Text>
   );
}
