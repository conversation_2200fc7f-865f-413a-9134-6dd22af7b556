import React, { useState } from 'react';
import {
   <PERSON><PERSON>,
   <PERSON><PERSON><PERSON>ontent,
   <PERSON><PERSON><PERSON>eader,
   <PERSON><PERSON>Title,
   DialogFooter,
} from '@/components/ui/dialog';
import {
   Select,
   SelectContent,
   SelectItem,
   SelectTrigger,
   SelectValue,
} from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { useAppSelector } from '../../store/store';
import { useDispatch } from 'react-redux';
import { closeModal } from '../../store/reducer/modal-reducer';

interface AccountSelectModalProps {
   title?: string;
   options: Record<string, string>;
   placeholder?: string;
   onSelect: (selectedValue: string | null) => void;
   confirmButtonText?: string;
   showCancelButton?: boolean;
}

const AccountSelectModal: React.FC = () => {
   const dispatch = useDispatch();
   const { payload } = useAppSelector((state) => state.modal);

   const {
      title = 'Select Account',
      options = {},
      placeholder = 'Choose an account',
      onSelect,
      confirmButtonText = 'Next',
      showCancelButton = true,
   } = (payload?.modalProps || {}) as AccountSelectModalProps;

   const [selectedValue, setSelectedValue] = useState<string>('');

   const handleSelectChange = (value: string) => {
      setSelectedValue(value);
   };

   const handleConfirm = () => {
      onSelect(selectedValue);
      dispatch(closeModal());
   };

   const handleCancel = () => {
      onSelect(null);
      dispatch(closeModal());
   };

   return (
      <Dialog open onOpenChange={() => handleCancel()}>
         <DialogContent
            className='max-w-lg bg-white dark:bg-zinc-900 shadow-2xl rounded-xl border-0 z-50 p-0 overflow-hidden'
            onInteractOutside={(e) => e.preventDefault()}
         >
            <div className='bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-zinc-800 dark:to-zinc-700 px-6 py-4 border-b border-gray-100 dark:border-zinc-600'>
               <DialogHeader className='space-y-0'>
                  <DialogTitle className='text-xl font-semibold text-gray-900 dark:text-white'>
                     {title}
                  </DialogTitle>
               </DialogHeader>
            </div>

            <div className='px-6 py-6 space-y-6'>
               <div className='space-y-2'>
                  <label className='text-sm font-medium text-gray-700 dark:text-gray-300 block'>
                     Select Account
                  </label>
                  <Select
                     value={selectedValue}
                     onValueChange={handleSelectChange}
                  >
                     <SelectTrigger
                        className={`w-full h-16 px-4 border-1 rounded-sm transition-all duration-200 ${'border-gray-200 dark:border-zinc-600 bg-gray-50 dark:bg-zinc-800 hover:border-blue-300  '} focus:ring-2 focus:ring-opacity-50`}
                     >
                        <SelectValue
                           placeholder={placeholder}
                           className='text-gray-900 dark:text-white font-medium'
                        />
                     </SelectTrigger>
                     <SelectContent className='bg-white dark:bg-zinc-800 border border-gray-200 dark:border-zinc-600 rounded-lg  shadow-lg'>
                        {Object.entries(options).map(([value, label]) => (
                           <SelectItem
                              key={value}
                              value={value}
                              className='px-2 py-2 hover:bg-blue-50 dark:hover:bg-zinc-700 cursor-pointer transition-colors duration-150 text-gray-900 dark:text-white'
                           >
                              <div className='flex items-center space-x-3'>
                                 <span className='font-medium'>{label}</span>
                              </div>
                           </SelectItem>
                        ))}
                     </SelectContent>
                  </Select>
               </div>
            </div>

            <div className=' px-6 py-4   dark:border-zinc-600'>
               <DialogFooter className='flex justify-end space-x-3'>
                  {showCancelButton && (
                     <Button
                        variant='outline'
                        onClick={handleCancel}
                        className='px-6 py-2 border-gray-300 dark:border-zinc-600 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-zinc-700 transition-colors duration-200'
                     >
                        Cancel
                     </Button>
                  )}
                  <Button
                     onClick={handleConfirm}
                     disabled={!selectedValue}
                     className={`px-6 py-2 font-medium transition-all duration-200 ${
                        !selectedValue
                           ? 'bg-gray-300 dark:bg-zinc-600 text-gray-500 dark:text-zinc-400 cursor-not-allowed'
                           : 'bg-blue-600 hover:bg-blue-500 text-white shadow-md hover:shadow-xl transform hover:-translate-y-0.5'
                     }`}
                  >
                     {confirmButtonText}
                  </Button>
               </DialogFooter>
            </div>
         </DialogContent>
      </Dialog>
   );
};

export default AccountSelectModal;
