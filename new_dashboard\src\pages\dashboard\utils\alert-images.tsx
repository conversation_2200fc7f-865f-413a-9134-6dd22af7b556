import AmazonAds from '../../../assets/icons/kpi/amazon-ads.png';
import AmazonSellingPartner from '../../../assets/icons/kpi/amazon-seller.png';
import FacebookAds from '../../../assets/icons/kpi/meta.png';
import GoogleAds from '../../../assets/icons/kpi/gads.png';
import Shopify from '../../../assets/icons/kpi/shopify.png';
import Web from '../../../assets/icons/kpi/web.png';


const alerts_category:{
   [key: string]: string;
}={
   amazon_ads: AmazonAds,
   amazon_selling_partner:AmazonSellingPartner,
   facebookads: FacebookAds,
   googleads: GoogleAds,
   store: Shopify,
   web: Web,
};
function alertCategoryImages(props: { category: string }) {
   const { category } = props;
   if (alerts_category[category]) {
      return <img src={alerts_category[category]} width='25px' height='25px' alt={category} />;
   }
   return (
      <div
        style={{
          width: '25px',
          height: '25px',
          backgroundColor: '#ddd',
          borderRadius: '4px',
        }}
      />
    );
}

export default alertCategoryImages;
