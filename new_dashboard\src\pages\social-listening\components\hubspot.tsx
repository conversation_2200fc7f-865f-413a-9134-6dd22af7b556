import FivetranConnectorWrapper from './fivetran-connector-wrapper';
import image from '../images/integrations/hubs6.png';
import { connectToHubspotSentiment } from '../utils';

import { useColorMode } from '@chakra-ui/react';

const Hubspot = () => {
   const { colorMode } = useColorMode();

   return (
      <FivetranConnectorWrapper
         imageSrc={colorMode === 'dark' ? image : image} // later will change with dark image
         heading='Hubspot'
         channelType='HUBSPOT'
         connectToSentimentFn={connectToHubspotSentiment}
         modalData={{
            heading: 'Connect Hubspot',
            content:
               'You are being redirected to Hubspot to connect your account...',
         }}
      />
   );
};

export default Hubspot;
