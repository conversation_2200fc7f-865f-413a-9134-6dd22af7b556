import {
   <PERSON><PERSON>,
   <PERSON><PERSON>,
   <PERSON>kel<PERSON>,
   <PERSON>ack,
   Tab,
   <PERSON><PERSON><PERSON><PERSON>,
   TabPanel,
   TabPanels,
   Tabs,
   Text,
   VStack,
} from '@chakra-ui/react';
import ModalWrapper from './modal-wrapper';
import KPIQueryKeys from '../../pages/dashboard/utils/query-keys';
import { useApiQuery } from '../../hooks/react-query-hooks';
import kpiService from '../../api/service/kpi/index';
import './card-view-details.scss';
import { useAppSelector, useAppDispatch } from '../../store/store';
import XI_icon from '../../assets/icons/marco-response-icon.svg';
import { Value } from '@/pages/dashboard/components/Value';
import MultiLineChart from '@/pages/dashboard/components/MultiChart';
import {
   KpiDataWithMeta,
   SummaryProp,
} from '@/pages/dashboard/utils/interface';
import BarChart from '@/pages/dashboard/components/BarChart';
import Breakdown from '@/pages/dashboard/components/breakdown';
import { useNavigate } from 'react-router-dom';
import { closeModal } from '../../store/reducer/modal-reducer';
import { LocalStorageService, Keys } from '../../utils/local-storage';
import { AuthUser } from '../../types/auth';
import {
   META_KPI_PROMPTS,
   GOOGLE_ADS_KPI_PROMPTS,
   MINIMUM_DATE_DIFFERENCE_DAYS,
} from '../../pages/marco/utils/analytics-agent/constants';
import {
   setCurrentSessionID,
   setCurrentMode,
   setKpiPrompts,
   setKpiMetadata,
   clearKpiMetadata,
} from '../../store/reducer/analytics-agent-reducer';
import { getCurrencyThreshold } from '../../pages/pulse/utils/helper';
import { getLabel } from '@/pages/dashboard/utils/helpers';

function KPIViewModal() {
   const dispatch = useAppDispatch();
   const navigate = useNavigate();
   const currentModal = useAppSelector((state) => state.modal);
   const { dateRange, prevRange } = useAppSelector((state) => state.kpi);
   const { generalSettings } = useAppSelector((state) => state.settings);
   // const { isAnalyticsAgentRunning } = useAppSelector(
   //    (state) => state.analyticsAgent,
   // );

   const kpidetails: KpiDataWithMeta =
      typeof currentModal.payload?.modalProps?.kpiDetails === 'object' &&
      currentModal.payload?.modalProps?.kpiDetails !== null
         ? (currentModal.payload?.modalProps?.kpiDetails as KpiDataWithMeta)
         : ({} as KpiDataWithMeta);

   // const isAnomaly = currentModal.payload?.modalProps?.isAnomaly;

   // const current_allData = (kpidetails.current_allData as unknown) || [];

   // const prev_allData = (kpidetails.previous_allData as unknown) || [];

   const { data, isFetching, errorMessage } = useApiQuery({
      queryKey: [KPIQueryKeys.kpiSummary, kpidetails.kpi_display_name],
      queryFn: () =>
         kpiService.getKpiSummary({
            data: kpidetails.current_allData.map((x) => ({
               xaxis: x.date,
               yaxis: x.kpi_value,
            })),
            kpi: kpidetails.kpi_display_name,
            category: kpidetails.category,
            currency: kpidetails.kpi_type,
         }),
      refetchOnWindowFocus: false,
      getInitialData: () => '',
   });

   const summary = data?.split('\n') || [];
   const startLabel = `${getLabel({ startDate: new Date(dateRange.start), endDate: new Date(dateRange.end) }, true)}`;
   const endLabel = `${getLabel({ startDate: new Date(prevRange.start), endDate: new Date(prevRange.end) }, true)}`;

   const handleAnomalyClick = async () => {
      if (kpidetails?.current_allData?.length > 0) {
         const dates = kpidetails.current_allData.map((x) => String(x.date));
         const startDate = (dates[0] || '').split('T')[0];
         const endDate = (dates[dates.length - 1] || '').split('T')[0];

         const userDetails = LocalStorageService.getItem<AuthUser>(
            Keys.FlableUserDetails,
         );
         const client_id = userDetails?.client_id ?? '';

         // Resolve KPI key directly from domain-aligned names
         const isGoogleAds = kpidetails.category === 'googleads';
         const promptSet = isGoogleAds
            ? GOOGLE_ADS_KPI_PROMPTS
            : META_KPI_PROMPTS;

         const rawName = String(kpidetails.kpi_names || '').trim();
         const normalized = rawName
            .toLowerCase()
            .replace(/\s+/g, '_')
            .replace(/%/g, '');
         // Handle simple plural → singular for common cases (e.g., video_views → video_view)
         const singular = normalized.endsWith('s')
            ? normalized.slice(0, -1)
            : normalized;
         const tryCandidates = [rawName, normalized, singular];

         let kpi = '';
         for (const cand of tryCandidates) {
            if (cand in promptSet) {
               kpi = cand as keyof typeof promptSet as string;
               break;
            }
         }
         if (!kpi) {
            console.error('No matching KPI key found for prompt set from', {
               kpi_names: kpidetails.kpi_names,
               kpi_display_name: kpidetails.kpi_display_name,
               tried: tryCandidates,
               category: kpidetails.category,
            });
            return;
         }

         const currency =
            String(generalSettings?.currency || '')
               .trim()
               .toUpperCase() || 'INR';
         const currencyThreshold = await getCurrencyThreshold(currency);

         // Normalize all dates to YYYY-MM-DD format
         const normalizedStartDate = String(startDate).split(/[T\s]/)[0];
         const normalizedEndDate = String(endDate).split(/[T\s]/)[0];
         const normalizedPrevStart = String(prevRange.start).split(/[T\s]/)[0];
         const normalizedPrevEnd = String(prevRange.end).split(/[T\s]/)[0];

         const currentValue = Number(kpidetails.current_total_value || 0);
         const previousValue = Number(kpidetails.previous_total_value || 0);

         let percentageChange = 0;
         let changeDirection = 'no change';

         if (previousValue !== 0) {
            percentageChange =
               ((currentValue - previousValue) / previousValue) * 100;
            changeDirection = percentageChange > 0 ? 'increase' : 'decrease';
         } else if (currentValue !== 0) {
            percentageChange = 100;
            changeDirection = 'increase';
         } else {
            percentageChange = 0;
            changeDirection = 'no change';
         }

         const absPercentageChange = Math.abs(percentageChange).toFixed(1);
         const platformName =
            kpidetails.category === 'googleads' ? 'Google Ads' : 'Meta Ads';

         const displayStart = String(startDate).split(/[T\s]/)[0];
         const displayEnd = String(endDate).split(/[T\s]/)[0];
         const displayPrevStart = String(prevRange.start).split(/[T\s]/)[0];
         const displayPrevEnd = String(prevRange.end).split(/[T\s]/)[0];
         const displayPrompt = `Diagnostic analysis: Root cause of why ${kpi} ${changeDirection} by ${absPercentageChange}% in ${platformName} from ${displayStart} to ${displayEnd} compared to ${displayPrevStart} to ${displayPrevEnd}?`;

         const buildAiPrompt = (
            client_id: string,
            kpi: string,
            start_date: string,
            end_date: string,
            currency: string,
         ): string => {
            // Use stored YYYY-MM-DD from local strings to avoid UTC shift
            const prev_start_str = String(prevRange.start).split('T')[0];
            const prev_end_str = String(prevRange.end).split('T')[0];

            const start = new Date(start_date);
            const end = new Date(end_date);
            const dateDiffInDays = Math.ceil(
               (end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24),
            );
            const isDateDiffGe6 =
               dateDiffInDays >= MINIMUM_DATE_DIFFERENCE_DAYS;

            const isGoogleAds = kpidetails.category === 'googleads';
            let kpiPrompt: string | undefined;
            if (isGoogleAds) {
               kpiPrompt =
                  GOOGLE_ADS_KPI_PROMPTS[
                     kpi as keyof typeof GOOGLE_ADS_KPI_PROMPTS
                  ];
            } else {
               kpiPrompt =
                  META_KPI_PROMPTS[kpi as keyof typeof META_KPI_PROMPTS];
            }

            if (!kpiPrompt) {
               console.error(
                  'No prompt template found for KPI:',
                  kpi,
                  'in category:',
                  kpidetails.category,
               );
               return '';
            }

            let processedPrompt = kpiPrompt
               .replace(/\{\{client_id\}\}/g, client_id)
               .replace(/\{\{KPI\}\}/g, kpi)
               .replace(/\{\{start_date\}\}/g, start_date)
               .replace(/\{\{end_date\}\}/g, end_date)
               .replace(/\{\{prev_start_str\}\}/g, prev_start_str)
               .replace(/\{\{prev_end_str\}\}/g, prev_end_str)
               .replace(/\{\{currency\}\}/g, currency)
               .replace(/\{\{currency_threshold\}\}/g, currencyThreshold);

            if (!isDateDiffGe6) {
               processedPrompt = processedPrompt.replace(
                  /\{\{#if date_diff_ge_6\}\}([\s\S]*?)\{\{\/if\}\}/g,
                  '',
               );
            } else {
               processedPrompt = processedPrompt.replace(
                  /\{\{#if date_diff_ge_6\}\}([\s\S]*?)\{\{\/if\}\}/g,
                  '$1',
               );
            }

            return processedPrompt;
         };

         const aiPrompt = buildAiPrompt(
            client_id,
            kpi,
            normalizedStartDate,
            normalizedEndDate,
            currency,
         );

         if (aiPrompt) {
            dispatch(setCurrentSessionID(''));
            dispatch(setCurrentMode('data-analyst'));

            dispatch(closeModal());

            const kpiMetadata = {
               kpi: String(kpi),
               currency: String(currency),
               currency_threshold: currencyThreshold,
               start_date: normalizedStartDate,
               end_date: normalizedEndDate,
               prev_start_date: normalizedPrevStart,
               prev_end_date: normalizedPrevEnd,
               percentage_change: String(percentageChange),
               source: 'root-cause' as const,
               platform: isGoogleAds
                  ? ('GOOGLE_ADS_KPI_PROMPTS' as const)
                  : ('META_KPI_PROMPTS' as const),
            };

            // Clear previous KPI metadata and store new metadata in Redux
            dispatch(clearKpiMetadata());
            dispatch(setKpiMetadata(kpiMetadata));

            dispatch(
               setKpiPrompts({
                  displayPrompt,
                  aiPrompt,
               }),
            );

            navigate('/marco/analytics-agent');
         } else {
            console.error('Failed to generate AI prompt for KPI:', kpi);
         }
      }
   };

   // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
   const anomaly: boolean | null = currentModal.payload?.modalProps?.anomaly;

   const isAnomalyDefined = anomaly !== undefined && anomaly !== null;

   const isSupportedCategory =
      kpidetails.category === 'facebookads' ||
      kpidetails.category === 'googleads';
   const showRootCause = isAnomalyDefined && isSupportedCategory;

   return (
      <ModalWrapper
         heading={kpidetails.kpi_display_name || 'KPI Details'}
         parentClassName='kpi-more-details'
      >
         <Flex justifyContent={'space-between'}>
            <VStack alignItems={'left'}>
               <Text fontSize={'12px'}>{startLabel}</Text>
               <Value
                  totalVal={kpidetails.current_total_value}
                  kpi_names={kpidetails.kpi_names}
                  kpi_unit={kpidetails.kpi_type}
                  kpi_type={kpidetails.kpi_type}
                  fontSize={16}
               />
            </VStack>
            <VStack alignItems={'right'}>
               <Text fontSize={'12px'}>{endLabel}</Text>
               <Value
                  totalVal={kpidetails.previous_total_value}
                  kpi_names={kpidetails.kpi_names}
                  kpi_unit={kpidetails.kpi_type}
                  kpi_type={kpidetails.kpi_type}
                  fontSize={16}
               />
            </VStack>
         </Flex>
         <Tabs>
            <TabList>
               <Tab>Current</Tab>
               <Tab isDisabled={!kpidetails?.current_allData?.length}>
                  Comparison
               </Tab>
               <Tab
                  isDisabled={
                     !kpidetails?.daily_breakdown ||
                     Object.keys(kpidetails.daily_breakdown).length === 0
                  }
               >
                  Breakdown
               </Tab>
            </TabList>

            <TabPanels>
               <TabPanel p={0} pt={2}>
                  <BarChart
                     kpiDetails={kpidetails}
                     value={kpidetails.current_allData}
                  />
               </TabPanel>
               <TabPanel p={0} pt={2}>
                  <MultiLineChart
                     kpiDetails={kpidetails}
                     // prevDetails={prevKpi}
                  />
               </TabPanel>
               <TabPanel p={0} pt={2}>
                  <Breakdown
                     data={
                        kpidetails.daily_breakdown
                           ? (Object.values(kpidetails.daily_breakdown)[0]
                                ?.sub_items ?? null)
                           : null
                     }
                  />
               </TabPanel>
            </TabPanels>
         </Tabs>

         <Flex direction={'column'} gap={1} margin={'30px 0'}>
            <Flex alignItems='center' gap={1} className='summaries'>
               <img src={XI_icon} alt='Insight Icon' className='insight-icon' />

               <Heading as='h4' size='l' marginBottom={2}>
                  Summary
               </Heading>
               {showRootCause && (
                  <button
                     className='find-root-cause-btn'
                     onClick={() => {
                        void handleAnomalyClick();
                     }}
                  >
                     <svg
                        width='40'
                        viewBox='0 0 27 28'
                        fill='none'
                        xmlns='http://www.w3.org/2000/svg'
                     >
                        <path
                           d='M4.91571 15.5793C4.06586 15.4292 4.06586 14.1885 4.91571 14.0383C6.42048 13.7721 7.81316 13.0562 8.91622 11.9821C10.0193 10.908 10.7827 9.5242 11.1093 8.00721L11.1601 7.76843C11.3441 6.91423 12.5402 6.9093 12.7315 7.76104L12.7944 8.03921C13.1335 9.54934 13.9039 10.9239 15.0088 11.9904C16.1138 13.0568 17.504 13.7676 19.0049 14.0334C19.8596 14.186 19.8596 15.4316 19.0049 15.5843C17.5039 15.8497 16.1135 16.5604 15.0085 17.6269C13.9035 18.6934 13.1332 20.0682 12.7944 21.5784L12.7315 21.8566C12.5402 22.7083 11.3441 22.7034 11.1601 21.8492L11.1093 21.6104C10.7827 20.0934 10.0193 18.7097 8.91622 17.6356C7.81316 16.5614 6.42048 15.8456 4.91571 15.5793Z'
                           fill='white'
                        />
                        <path
                           d='M14.4588 7.35661C14.2038 7.31156 14.2038 6.93936 14.4588 6.89431C14.9102 6.81443 15.328 6.59968 15.6589 6.27744C15.9899 5.9552 16.2189 5.54007 16.3169 5.08498L16.3321 5.01334C16.3873 4.75708 16.7461 4.7556 16.8035 5.01113L16.8224 5.09458C16.9241 5.54761 17.1553 5.95999 17.4867 6.27993C17.8182 6.59986 18.2353 6.81309 18.6856 6.89283C18.942 6.93862 18.942 7.3123 18.6856 7.35809C18.2352 7.43774 17.8181 7.65093 17.4866 7.97088C17.1551 8.29083 16.9241 8.70326 16.8224 9.15634L16.8035 9.23979C16.7461 9.49531 16.3873 9.49384 16.3321 9.23758L16.3169 9.16594C16.2189 8.71085 15.9899 8.29572 15.6589 7.97348C15.328 7.65124 14.9102 7.43649 14.4588 7.35661Z'
                           fill='white'
                        />
                        <path
                           d='M19.0044 9.58149C18.8345 9.55146 18.8345 9.30332 19.0044 9.27329C19.3054 9.22004 19.5839 9.07687 19.8045 8.86204C20.0251 8.64722 20.1778 8.37047 20.2431 8.06707L20.2533 8.01931C20.2901 7.84847 20.5293 7.84749 20.5676 8.01783L20.5802 8.07347C20.648 8.37549 20.8021 8.65041 21.0231 8.8637C21.244 9.07699 21.5221 9.21914 21.8223 9.2723C21.9932 9.30283 21.9932 9.55195 21.8223 9.58248C21.5221 9.63557 21.244 9.77771 21.023 9.99101C20.802 10.2043 20.6479 10.4793 20.5802 10.7813L20.5676 10.8369C20.5293 11.0073 20.2901 11.0063 20.2533 10.8355L20.2431 10.7877C20.1778 10.4843 20.0251 10.2076 19.8045 9.99274C19.5839 9.77791 19.3054 9.63474 19.0044 9.58149Z'
                           fill='white'
                        />
                        <path
                           d='M10.7943 16.2241C10.7181 16.2241 10.6556 16.2043 10.6069 16.1646C10.5581 16.122 10.5292 16.0702 10.52 16.0092C10.5139 15.9452 10.5353 15.8812 10.584 15.8172L11.6309 14.4229V14.6744L10.6343 13.3395C10.5855 13.2725 10.5642 13.2085 10.5703 13.1475C10.5764 13.0835 10.6038 13.0317 10.6526 12.9921C10.7014 12.9494 10.7623 12.9281 10.8354 12.9281C10.8964 12.9281 10.9497 12.9433 10.9954 12.9738C11.0442 13.0012 11.093 13.0485 11.1417 13.1155L11.9646 14.2492H11.7772L12.5954 13.1155C12.6442 13.0485 12.6914 13.0012 12.7372 12.9738C12.7859 12.9433 12.8423 12.9281 12.9063 12.9281C12.9825 12.9281 13.045 12.9479 13.0937 12.9875C13.1425 13.0271 13.1699 13.0789 13.176 13.1429C13.1821 13.2039 13.1593 13.2694 13.1074 13.3395L12.1109 14.6744V14.4229L13.1532 15.8172C13.2019 15.8812 13.2233 15.9452 13.2172 16.0092C13.2111 16.0702 13.1836 16.122 13.1349 16.1646C13.0861 16.2043 13.0221 16.2241 12.9429 16.2241C12.885 16.2241 12.8316 16.2088 12.7829 16.1784C12.7372 16.1479 12.6884 16.0991 12.6366 16.0321L11.768 14.8435H11.9692L11.1052 16.0321C11.0564 16.0991 11.0076 16.1479 10.9589 16.1784C10.9101 16.2088 10.8553 16.2241 10.7943 16.2241ZM14.0486 16.2241C13.9541 16.2241 13.881 16.1982 13.8292 16.1464C13.7804 16.0915 13.756 16.0168 13.756 15.9224V13.2298C13.756 13.1323 13.7804 13.0576 13.8292 13.0058C13.881 12.954 13.9541 12.9281 14.0486 12.9281C14.1431 12.9281 14.2147 12.954 14.2634 13.0058C14.3153 13.0576 14.3412 13.1323 14.3412 13.2298V15.9224C14.3412 16.0168 14.3168 16.0915 14.268 16.1464C14.2193 16.1982 14.1461 16.2241 14.0486 16.2241Z'
                           fill='#3E75DC'
                        />
                     </svg>

                     <span className='text'>Find root cause</span>
                  </button>
               )}
            </Flex>

            <Summary
               summary={summary}
               isFetching={isFetching}
               error={typeof errorMessage === 'string' ? errorMessage : null}
            />
         </Flex>
      </ModalWrapper>
   );
}
function Summary(props: SummaryProp) {
   const { summary, isFetching, error } = props;
   if (error) {
      return <Text color={'red'}>{error}</Text>;
   }
   if (!isFetching)
      return summary.map((x) => (
         <Text key={x} fontSize={'14px'}>
            {x}
         </Text>
      ));
   return (
      <Stack>
         <Skeleton height='10px' />
         <Skeleton height='10px' width={'50%'} />
         <Skeleton height='10px' />
         <Skeleton height='10px' width={'50%'} />
      </Stack>
   );
}

export default KPIViewModal;
