// ./components/cards/DashboardCardSkeleton.tsx
import { Box, Skeleton } from '@chakra-ui/react';

const DashboardCardSkeleton = ({ label }: { label: string }) => {
   return (
      <Box mb={6}>
         {/* Section Title Skeleton */}
         <Skeleton height='25px' width='150px' mb={4}>
            {label}
         </Skeleton>

         {/* Grid of KPI Skeletons */}
         <Box display='flex' flexWrap='wrap' gap={4}>
            {Array.from({ length: 8 }).map((_, i) => (
               <Box
                  key={i}
                  width='24%' // ~4 per row
                  minWidth='150px'
                  height='200px'
                  borderRadius='8px'
                  overflow='hidden'
                  boxShadow='sm'
               >
                  <Skeleton height='100%' width='100%' />
               </Box>
            ))}
         </Box>
      </Box>
   );
};

export default DashboardCardSkeleton;
