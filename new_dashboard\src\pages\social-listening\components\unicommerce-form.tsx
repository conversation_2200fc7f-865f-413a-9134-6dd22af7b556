/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/no-unsafe-member-access */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */
/* eslint-disable @typescript-eslint/no-misused-promises */
import React, { useState, useCallback, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { connectDisconnectToUnicommerce } from '../utils';
import { channelNames } from '../utils/constant';
import { Keys, LocalStorageService } from '../../../utils/local-storage';
import image from '../images/integrations/unicommerce.jpg';
import { NewCommerceIntegrationLayout } from './seller-panel-components/new-commerce-integration-layout';
import { UnicommerceSellerPanel } from './seller-panel-components/unicommerce-panel';
import { unicommerceIntegrationSteps } from '../utils/constant';
import { AuthUser } from '../../../types/auth';

interface FormFields {
   channelName: string;
   username: string;
   password: string;
   tenantId: string;
   facility: string;
}

interface ApiError {
   success: boolean;
   message: string;
}

const UnicommerceForm: React.FC = () => {
   const navigate = useNavigate();
   const [trying, setTrying] = useState<boolean>(false);
   const [apiError, setApiError] = useState<ApiError | null>(null);

   const client_id = LocalStorageService.getItem<AuthUser>(
      Keys.FlableUserDetails,
   )?.client_id;

   const [unmounted, setUnmounted] = useState<boolean>(false);

   const defaultState: FormFields = {
      channelName: channelNames.UNICOMMERCE,
      username: '',
      password: '',
      tenantId: '',
      facility: '',
   };

   const [formFields, setFormFields] = useState<FormFields>(defaultState);

   const handleChange = useCallback(
      (e: React.ChangeEvent<HTMLInputElement>) => {
         if (unmounted) return;
         const { name, value } = e.target;
         setFormFields((prev) => ({ ...prev, [name]: value }));
      },
      [unmounted],
   );

   const handleConnect = useCallback(
      async (e: React.FormEvent<HTMLFormElement>) => {
         if (!client_id) return;
         e.preventDefault();
         if (unmounted) return;
         const { username, password, tenantId, facility } = formFields;
         try {
            setTrying(true);
            setApiError({
               success: false,
               message: '',
            });

            await connectDisconnectToUnicommerce({
               channel_name: channelNames.UNICOMMERCE,
               client_id,
               username,
               password,
               tenant_id: tenantId,
               facility,
               isConnect: true,
            });
            setFormFields(defaultState);
            setApiError({
               success: true,
               message: 'Connection Established, Redirecting...',
            });
            setTimeout(() => {
               navigate('/integrations');
            }, 3000);
         } catch (err) {
            const error = err as any;

            const errMessage =
               error.response?.data?.message ||
               'Error connecting to Unicommerce';
            setApiError({
               success: false,
               message: errMessage,
            });
         } finally {
            setTrying(false);
         }
      },
      [formFields, history, defaultState, unmounted],
   );

   useEffect(() => {
      setUnmounted(false);
      return () => {
         setUnmounted(true);
         setFormFields(defaultState);
         setApiError(null);
      };
   }, []);

   return (
      <NewCommerceIntegrationLayout
         title='Unicommerce'
         description='Connect your  Unicommerce  account to manage your logistics seamlessly'
         logo={image}
         logoAlt='Unicommerce Logo'
         steps={unicommerceIntegrationSteps}
      >
         <UnicommerceSellerPanel
            title='Account Credentials'
            description='Please provide the following credentials for Unicommerce:'
            username={formFields.username}
            password={formFields.password}
            tenantId={formFields.tenantId}
            facility={formFields.facility}
            onChange={handleChange}
            onSubmit={handleConnect}
            isLoading={trying}
            apiResponse={apiError}
            submitButtonText='Connect to Unicommerce'
            loadingText='Connecting to Unicommerce...'
         />
      </NewCommerceIntegrationLayout>
   );
};

export default UnicommerceForm;
