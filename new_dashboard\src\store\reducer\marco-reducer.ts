import { AnalyticsAgentChat } from '@/api/service/agentic-workflow/analytics-agent';
import { createSlice, PayloadAction } from '@reduxjs/toolkit';

export type Agents =
   | 'analytics-agent'
   | 'meta-ads-manager-agent'
   | 'meta-ads-manager-auto';

interface InitialState {
   currentAgent: Agents;
   currentHistory: {
      'analytics-agent': AnalyticsAgentChat[];
   };
}

const initialState: InitialState = {
   currentAgent: 'analytics-agent',
   currentHistory: {
      'analytics-agent': [],
   },
};

const marcoSlice = createSlice({
   name: 'marco',
   initialState,
   reducers: {
      setCurrentAgent: (state, action: PayloadAction<Agents>) => {
         state.currentAgent = action.payload;
      },
      setCurrentHistory: (
         state,
         action: PayloadAction<{
            agent: Agents;
            history: AnalyticsAgentChat[];
         }>,
      ) => {
         const { agent, history } = action.payload;

         const merged = [
            ...state.currentHistory[agent as 'analytics-agent'],
            ...history,
         ].filter((chat) => chat.session_id);

         const uniqueBySessionId = Array.from(
            new Map(merged.map((chat) => [chat.session_id, chat])).values(),
         );

         if (agent === 'analytics-agent') {
            state.currentHistory['analytics-agent'] = uniqueBySessionId;
         }
      },
   },
});

export const { setCurrentAgent, setCurrentHistory } = marcoSlice.actions;

export default marcoSlice.reducer;
