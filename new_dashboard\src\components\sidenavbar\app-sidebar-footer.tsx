import settingsIcon from '@/assets/icons/settings-sidebar-icon.svg';
import {
   SidebarGroup,
   SidebarMenu,
   SidebarGroupContent,
   SidebarMenuButton,
   SidebarMenuItem,
   useSidebar,
} from '@/components/ui/sidebar';
import { useAppSelector } from '@/store/store';
import { AuthUser } from '@/types/auth';
import { Keys, LocalStorageService } from '@/utils/local-storage';
import { useNavigate } from 'react-router-dom';
import ProgressCircle from '../progress-circle/progress-circle';
import { ReactNode } from 'react';
import { cn } from '@/utils';

interface NAVS {
   title: string;
   url: string;
   icon: string | ReactNode;
}

const AppSidebarFooter = () => {
   const navigate = useNavigate();

   const { open } = useSidebar();
   const { optimisationsStatus } = useAppSelector((state) => state.onboarding);

   const userDetails = LocalStorageService.getItem(
      Keys.FlableUserDetails,
   ) as AuthUser;

   const handleClick = (url: string) => {
      navigate(url);
   };

   const entries = Object.entries(optimisationsStatus).filter(
      ([key]) => key !== 'complete',
   );
   const totalKeys = entries.length;
   const trueKeys = entries.filter(([, status]) => status).length;
   const percentage = (trueKeys / totalKeys) * 100;

   const APP_SIDEBAR_FOOTER: NAVS[] = [];

   if (userDetails?.user_role === 'Admin') {
      !optimisationsStatus.complete &&
         APP_SIDEBAR_FOOTER.push({
            title: 'Optimisations',
            url: '/optimisations',
            icon: (
               <ProgressCircle
                  value={percentage}
                  size='20px'
                  color='green'
                  label={`${percentage.toFixed(0)}%`}
               />
            ),
         });

      APP_SIDEBAR_FOOTER.push({
         title: 'Settings',
         url: '/settings',
         icon: settingsIcon,
      });
   }

   return (
      <SidebarGroup>
         <SidebarGroupContent>
            <SidebarMenu>
               {APP_SIDEBAR_FOOTER.map((item) => (
                  <SidebarMenuItem key={item.title}>
                     <SidebarMenuButton
                        className='mb-4 mx-auto hover:cursor-pointer'
                        asChild
                        tooltip={{
                           children: item.title,
                           className:
                              'z-100 bg-[#3c76e1] rounded-sm px-2 py-1 ml-1 text-white [&_svg]:fill-[#3c76e1]',
                        }}
                        onClick={() => handleClick(item.url)}
                     >
                        <div className='flex items-center gap-2'>
                           {typeof item.icon === 'string' ? (
                              <img
                                 src={item.icon}
                                 alt={`${item.title} icon`}
                                 className={cn(
                                    open ? 'w-[24px]' : 'w-[26px]',
                                    'text-black',
                                 )}
                              />
                           ) : (
                              item.icon
                           )}
                           <span className='text-[16px] text-black font-bold'>
                              {item.title}
                           </span>
                        </div>
                     </SidebarMenuButton>
                  </SidebarMenuItem>
               ))}
            </SidebarMenu>
         </SidebarGroupContent>
      </SidebarGroup>
   );
};

export default AppSidebarFooter;
