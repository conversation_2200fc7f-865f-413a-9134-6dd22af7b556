import React, { useEffect, useState } from 'react';
import Card from './Card';
import image from '../images/integrations/meta_ads_logo.png';
import SocialListeningQueryKeys from '../utils/query-keys';
import endPoints from '../apis/agent';
import { useNavigate } from 'react-router-dom';
import { useApiMutation, useApiQuery } from '../../../hooks/react-query-hooks';
import { channelNames } from '../utils/constant';
import { Keys, LocalStorageService } from '../../../utils/local-storage';
import { connectToFbAds } from '../utils';
import { useToast } from '@chakra-ui/react';
import { socialListeningStrings } from '../../../utils/strings/social-listening-strings';
import { AuthUser } from '../../../types/auth';
import Swal from 'sweetalert2';
import Config from '../../../config';
import { ApiError } from './facebook-ads-form';
import { Loading } from '../../../components';

// Custom hook for fetching connection details
const useFacebookAdsConnectionDetails = (client_id: string | undefined) => {
   return useApiQuery({
      queryKey: [SocialListeningQueryKeys.connectionDetailsFacebookAds],
      queryFn: () =>
         endPoints.checkConnectionDetails({
            client_id: client_id!,
            channel_name: channelNames.FACEBOOK_ADS,
         }),
   });
};

// Custom hook for disconnecting
const useDisconnectFacebookAds = () => {
   const toast = useToast();
   return useApiMutation({
      queryKey: [SocialListeningQueryKeys.disconnectFbAdsSentiment],
      mutationFn: connectToFbAds,
      onSuccessHandler: (data) => {
         console.log('Disconnected successfully', data);
      },
      onError: (error) => {
         toast({
            title: socialListeningStrings.disconnectFailed,
            description: error,
            status: 'error',
            duration: 5000,
            isClosable: true,
         });
      },
   });
};

const FacebookAdsV1 = () => {
   const navigate = useNavigate();
   const client_id = LocalStorageService.getItem<AuthUser>(
      Keys.FlableUserDetails,
   )?.client_id;

   const { data, isLoading, errorMessage } =
      useFacebookAdsConnectionDetails(client_id);
   const {
      mutate,
      isPending: isDisconnectingFbAds,
      errorMessage: disconnectionErrorMessage,
   } = useDisconnectFacebookAds();

   const handleDisconnect = () => {
      if (!client_id) return;
      mutate({
         channel_name: channelNames.FACEBOOK_ADS,
         client_id,
         isConnect: false,
      });
   };

   const isConnected = !!data?.details?.is_active;

   return (
      <Card
         heading='Meta Ads'
         error={errorMessage || disconnectionErrorMessage}
         src={image}
         onButtonClick={
            isConnected
               ? handleDisconnect
               : () => navigate('/integrations/facebook-ads')
         }
         isFetching={isLoading}
         isDisconnecting={isDisconnectingFbAds}
         isConnected={isConnected}
      />
   );
};

const FacebookAdsV2 = () => {
   const client_id = LocalStorageService.getItem<AuthUser>(
      Keys.FlableUserDetails,
   )?.client_id;

   const { data, isLoading, errorMessage } =
      useFacebookAdsConnectionDetails(client_id);
   const [sourceId, setSourceId] = useState('');
   const isConnected = !!data?.details?.is_active;
   const metaData = data?.details?.meta_data;

   const {
      mutate,
      isPending: isDisconnectingFbAds,
      errorMessage: disconnectionErrorMessage,
   } = useDisconnectFacebookAds();

   const handleDisconnect = async () => {
      if (!client_id) return;
      if (metaData?.source_id)
         await endPoints.deleteAirbyteSource(metaData.source_id);
      mutate({
         channel_name: channelNames.FACEBOOK_ADS,
         client_id,
         isConnect: false,
      });
   };

   const toast = useToast();

   function showToast(msg: string | null) {
      toast({
         title: socialListeningStrings.disconnectFailed,
         description: msg,
         status: 'error',
         duration: 5000,
         isClosable: true,
      });
   }

   const { isPending: isInitiatingSource, mutate: initiateSource } =
      useApiMutation({
         queryKey: ['initiate-oauth-source'],
         mutationFn: endPoints.initiateSource,
         onSuccessHandler: ({ authData: { consentUrl } }) => {
            window.open(consentUrl, '_blank');
         },
         onError: (error) => {
            showToast(error);
         },
      });

   const { isPending: isPendingSource, mutate: createSource } = useApiMutation({
      mutationFn: endPoints.createSource,
      onSuccessHandler: ({ sourceId }) => {
         console.log('SOURCE ID ', sourceId);
         setSourceId(sourceId);

         const payload = {
            name: 'facebooks-ads-connection-' + client_id,
            sourceId: sourceId,
            destinationId: Config.VITE_AIRBYTE_DESTINATION_ID_META_ADS,
            prefix: `${client_id?.toLowerCase()}_`,
         };

         createConnection(payload);
      },
      onError: (error) => {
         showToast(error);
      },
   });

   // create Connection
   const { isPending: isPendingConnection, mutate: createConnection } =
      useApiMutation({
         mutationFn: endPoints.createConnection,
         onSuccessHandler: async ({ connectionId }) => {
            console.log('CONNECTION ID ', connectionId);
            alert(connectionId);
            await handleSync(connectionId);
         },
         onError: (error) => {
            showToast(error);
         },
      });

   const handleSync = async (connectionId: string) => {
      try {
         await endPoints.triggerSync({ connectionId });
         await connectToFbAds({
            channel_name: channelNames.FACEBOOK_ADS,
            client_id: client_id!,
            isConnect: true,
            source_id: sourceId,
         });
         toast({
            title: 'Facebook ads connected',
            description: '',
            status: 'success',
            duration: 2000,
            isClosable: true,
         });
         setTimeout(() => {
            window.location.href = `${window.location.origin}/integrations`;
         }, 1000);
      } catch (err) {
         const error = err as ApiError;
         showToast(
            error.response.data.message || 'Error connecting to Facebook Ads',
         );
      }
   };

   useEffect(() => {
      async function handleSource() {
         const urlParams = new URLSearchParams(window.location.search);
         const secretId = urlParams.get('secret_id');

         if (secretId) {
            const { value, isDismissed } = (await Swal.fire({
               title: 'Enter Account Id',
               input: 'text',
               inputPlaceholder: 'Separated by commas..',
               showCancelButton: true,
            })) as { value: string; isDismissed: boolean };

            if (isDismissed) return;

            const accountIds = value.split(',').map((item) => item.trim());
            const { value: dateRange, isDismissed: dateDismissed } =
               (await Swal.fire({
                  title: 'Select Data Range',
                  input: 'select',
                  inputOptions: {
                     '15d': 'Last 15 days',
                     '1m': 'Last 1 month',
                     '3m': 'Last 3 months',
                     '6m': 'Last 6 months',
                     '1y': 'Last 1 year',
                  },
                  inputPlaceholder: 'Select a range',
                  showCancelButton: true,
               })) as {
                  value: '15d' | '1m' | '3m' | '6m' | '1y';
                  isDismissed: boolean;
               };

            if (dateDismissed || !dateRange) return;

            const payload = {
               workspaceId: Config.VITE_AIRBYTE_WORKSPACE_ID,
               accountIds,
               dateRange,
               sourceType: 'facebook-marketing',
               name: 'meta-source-' + client_id,
               secretId,
            };

            createSource(payload);
         }
      }

      void handleSource();
   }, []);

   const handleConnect = () => {
      initiateSource({ sourceType: 'facebook-marketing' });
   };

   const shouldLoad = isPendingSource || isPendingConnection;

   return (
      <>
         {shouldLoad && <Loading full background />}

         <Card
            heading='Meta Ads'
            src={image}
            error={errorMessage || disconnectionErrorMessage}
            isFetching={isLoading}
            isConnecting={isInitiatingSource || shouldLoad}
            isDisconnecting={isDisconnectingFbAds}
            onButtonClick={isConnected ? handleDisconnect : handleConnect}
            isConnected={isConnected}
         />
      </>
   );
};

const FacebookAds: React.FC = () => {
   const version = +Config.VITE_FACEBOOK_ADS_VERSION || 2; // Dynamic version handling
   const versionMap: Record<number, JSX.Element> = {
      1: <FacebookAdsV1 />,
      2: <FacebookAdsV2 />,
   };
   return versionMap[version] || null;
};

export default FacebookAds;
