import { AnalyticsAgentChat } from '@/api/service/agentic-workflow/analytics-agent';
import { UserSocialDetails } from '@/api/service/onboarding';
import {
   parseISO,
   isToday,
   isYesterday,
   isAfter,
   subDays,
   subMonths,
   isSameMonth,
} from 'date-fns';

export const convertToHTMLTable = (input: string): string => {
   const lines = input.trim().split('\n');
   if (lines.length < 2) return '';

   const headers = lines[0]
      .split('|')
      .map((h) => h.trim().toUpperCase())
      .filter(Boolean);

   const rows = lines.slice(2).map((line) =>
      line
         .split('|')
         .map((cell) => cell.trim())
         .filter(Boolean),
   );

   const html = `
    <table style="border-collapse: collapse; margin-top: 1rem; width: 100%;">
      <thead>
        <tr>
          ${headers.map((h) => `<th style="border: 1px solid #ccc; padding: 8px;">${h}</th>`).join('')}
        </tr>
      </thead>
      <tbody>
        ${rows
           .map(
              (row) =>
                 `<tr>${row
                    .map(
                       (cell) =>
                          `<td style="border: 1px solid #eee; padding: 8px;">${cell}</td>`,
                    )
                    .join('')}</tr>`,
           )
           .join('')}
      </tbody>
    </table>
  `;

   return html;
};

export const getSuggestionSubset = (suggestions: string[], count: number) => {
   const shuffled = [...suggestions].sort(() => 0.5 - Math.random());
   return shuffled.slice(0, count);
};

export const getSmartSuggestions = (
   connections: UserSocialDetails[],
   suggestionsMap: {
      web: string[];
      facebookads: string[];
      store: string[];
      googleads: string[];
   },
) => {
   const aliasMap: Record<string, string> = {
      shopify: 'store',
      flable_pixel: 'web',
   };

   const available = Array.from(
      new Set(
         connections.map(
            ({ channel_name }) => aliasMap[channel_name] || channel_name,
         ),
      ),
   ).filter(
      (ch) =>
         suggestionsMap[ch as keyof typeof suggestionsMap] &&
         suggestionsMap[ch as keyof typeof suggestionsMap].length > 0,
   );

   const result: string[] = [];

   if (available.length === 1) {
      result.push(
         ...getSuggestionSubset(
            suggestionsMap[available[0] as keyof typeof suggestionsMap],
            4,
         ),
      );
   } else if (available.length === 2) {
      result.push(
         ...getSuggestionSubset(
            suggestionsMap[available[0] as keyof typeof suggestionsMap],
            2,
         ),
      );
      result.push(
         ...getSuggestionSubset(
            suggestionsMap[available[1] as keyof typeof suggestionsMap],
            2,
         ),
      );
   } else if (available.length === 3) {
      result.push(
         ...getSuggestionSubset(
            suggestionsMap[available[0] as keyof typeof suggestionsMap],
            1,
         ),
      );
      result.push(
         ...getSuggestionSubset(
            suggestionsMap[available[1] as keyof typeof suggestionsMap],
            1,
         ),
      );
      result.push(
         ...getSuggestionSubset(
            suggestionsMap[available[2] as keyof typeof suggestionsMap],
            1,
         ),
      );

      const extraSource = getSuggestionSubset(available, 1)[0];
      result.push(
         ...getSuggestionSubset(
            suggestionsMap[extraSource as keyof typeof suggestionsMap],
            1,
         ),
      );
   } else {
      for (let i = 0; i < Math.min(4, available.length); i++) {
         result.push(
            ...getSuggestionSubset(
               suggestionsMap[available[i] as keyof typeof suggestionsMap],
               1,
            ),
         );
      }
   }

   return result;
};

type GroupedHistory = {
   today: AnalyticsAgentChat[];
   yesterday: AnalyticsAgentChat[];
   last7Days: AnalyticsAgentChat[];
   lastMonth: AnalyticsAgentChat[];
   thisMonth: AnalyticsAgentChat[];
   older: AnalyticsAgentChat[];
};

export const groupAgentChatsByDate = (
   chatList: AnalyticsAgentChat[],
): GroupedHistory => {
   const grouped: GroupedHistory = {
      today: [],
      yesterday: [],
      last7Days: [],
      thisMonth: [],
      lastMonth: [],
      older: [],
   };

   const now = new Date();

   for (const chat of chatList) {
      const date = parseISO(chat.updated_at);

      if (isToday(date)) {
         grouped.today.push(chat);
      } else if (isYesterday(date)) {
         grouped.yesterday.push(chat);
      }
      // Within last 7 days, but not today/yesterday
      else if (isAfter(date, subDays(now, 7))) {
         grouped.last7Days.push(chat);
      }
      // Same calendar month (but not in last 7 days)
      else if (isSameMonth(date, now)) {
         grouped.thisMonth.push(chat);
      }
      // Previous calendar month
      else if (isSameMonth(date, subMonths(now, 1))) {
         grouped.lastMonth.push(chat);
      }
      // Anything older
      else {
         grouped.older.push(chat);
      }
   }

   // Sort each group by most recent first
   for (const key in grouped) {
      grouped[key as keyof GroupedHistory].sort((a, b) => {
         const dateA = parseISO(a.updated_at).getTime();
         const dateB = parseISO(b.updated_at).getTime();
         return dateB - dateA;
      });
   }

   return grouped;
};

export const formatSqlForHtml = (rawSql: string): string => {
   if (!rawSql) return '';

   const escapeHtml = (text: string) =>
      text
         .replace(/&/g, '&amp;')
         .replace(/</g, '&lt;')
         .replace(/>/g, '&gt;')
         .replace(/"/g, '&quot;')
         .replace(/'/g, '&#39;');

   const normalized = rawSql.replace(/\s+/g, ' ').trim();

   const keywordRegex =
      /\b(SELECT|FROM|WHERE|GROUP BY|ORDER BY|HAVING|INNER JOIN|LEFT JOIN|RIGHT JOIN|FULL JOIN|CROSS JOIN|JOIN|ON|AND|OR|AS|IN|INSERT INTO|VALUES|UPDATE|SET|DELETE|CREATE TABLE|ALTER TABLE|DROP TABLE|LIMIT|OFFSET)\b/gi;

   const sqlWithNewlines = normalized.replace(keywordRegex, '\n$1').trim();

   const rawLines = sqlWithNewlines
      .split('\n')
      .map((l) => l.trim())
      .filter(Boolean);

   const getKeyword = (lineLower: string): string | null => {
      if (/^select\b/.test(lineLower)) return 'select';
      if (/^from\b/.test(lineLower)) return 'from';
      if (/^where\b/.test(lineLower)) return 'where';
      if (/^(group by|order by)\b/.test(lineLower)) return 'groupby';
      if (/^having\b/.test(lineLower)) return 'having';
      if (
         /^(inner join|left join|right join|full join|cross join|join)\b/.test(
            lineLower,
         )
      )
         return 'join';
      if (/^on\b/.test(lineLower)) return 'on';
      if (/^(and|or)\b/.test(lineLower)) return 'logical';
      if (/^insert into\b/.test(lineLower)) return 'insert';
      if (/^values\b/.test(lineLower)) return 'values';
      if (/^update\b/.test(lineLower)) return 'update';
      if (/^set\b/.test(lineLower)) return 'set';
      if (/^delete\b/.test(lineLower)) return 'delete';
      if (/^(create table|alter table|drop table)\b/.test(lineLower))
         return 'ddl';
      if (/^(limit|offset)\b/.test(lineLower)) return 'limit';
      return null;
   };

   const indentMap: Record<string, number> = {
      select: 0,
      from: 0,
      where: 1,
      groupby: 0,
      having: 1,
      join: 1,
      on: 2,
      logical: 2,
      insert: 0,
      values: 1,
      update: 0,
      set: 1,
      delete: 0,
      ddl: 0,
      limit: 0,
   };

   const lines: string[] = [];
   let prevKeyword: string | null = null;
   let prevIndent = 0;

   for (const rawLine of rawLines) {
      const lower = rawLine.toLowerCase();
      const kw = getKeyword(lower);

      let indent: number;
      if (kw) {
         indent = indentMap[kw] ?? 0;
      } else {
         if (prevKeyword === 'select') indent = 1;
         else indent = prevIndent;
      }

      const escaped = escapeHtml(rawLine);
      const nbsp = '&nbsp;'.repeat(indent * 4);
      lines.push(nbsp + escaped);

      prevKeyword = kw ?? prevKeyword;
      prevIndent = indent;
   }

   const highlighted = lines
      .join('<br/>')
      .replace(
         /\b(SELECT|FROM|WHERE|GROUP BY|ORDER BY|HAVING|AND|OR|INNER JOIN|LEFT JOIN|RIGHT JOIN|FULL JOIN|CROSS JOIN|JOIN|ON|AS|INSERT INTO|VALUES|UPDATE|SET|DELETE|CREATE TABLE|ALTER TABLE|DROP TABLE|LIMIT|OFFSET)\b/gi,
         (match) =>
            `<span style="color: #7c3aed; font-weight: bold;">${match.toUpperCase()}</span>`,
      );

   return `<details class="group rounded-xl px-1 py-2 transition-all duration-200">
               <summary class="flex items-center gap-1 cursor-pointer select-none font-semibold text-gray-800 text-xs list-none [&::-webkit-details-marker]:hidden">
                  <span class="text-blue-600">Show SQL</span>
                  <svg class="w-3.5 h-3.5 text-gray-500 shrink-0 transform transition-transform duration-200 group-open:rotate-90"
                        fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                     <path stroke-linecap="round" stroke-linejoin="round" d="M9 5l7 7-7 7"/>
                  </svg>
               </summary>
               <pre class="mt-2 text-sm text-gray-800 font-mono whitespace-pre-wrap px-3 rounded-lg overflow-x-auto">${highlighted}</pre>
            </details>`;
};

export const addTableStylesToHtmlString = (htmlString: string) => {
   const tableStyle = `
      width:100%;
      border: 1px solid #fff;
      border-collapse: collapse;
      border-spacing: 0;
      margin: 24px 0;
      font-family: 'Segoe UI', Tahoma, sans-serif;
      font-size: 15px;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 4px 12px rgba(0,0,0,0.08);
      background-color: #fff;
   `
      .replace(/\s+/g, ' ')
      .trim();

   const thStyle = `
      background-color: #e4f2ff;
      padding: 12px 16px;
      text-align: left;
      font-weight: 600;
      color: #000;
      border: 1px solid #ccc;
   `
      .replace(/\s+/g, ' ')
      .trim();

   const tdStyle = `
      padding: 12px 16px;
      color: #333;
      border: 1px solid #ddd;
      vertical-align: top;
   `
      .replace(/\s+/g, ' ')
      .trim();

   const trBaseStyle = `
      transition: background-color 0.2s ease;
   `
      .replace(/\s+/g, ' ')
      .trim();

   const rowColors = ['#ffffff', '#ffffff'];
   let rowIndex = 0;

   htmlString = htmlString.replace(/<tr\b(?![^>]*style=)/g, () => {
      const color = rowColors[rowIndex % rowColors.length];
      rowIndex++;
      return `<tr style="${trBaseStyle};background-color:${color}"`;
   });

   htmlString = htmlString.replace(
      /<tr\b([^>]*?)style="([^"]*?)"/g,
      (_, attrs, existing) => {
         const color = rowColors[rowIndex % rowColors.length];
         rowIndex++;
         return `<tr${attrs}style="${existing};${trBaseStyle};background-color:${color}"`;
      },
   );

   // Apply styles to table, th, td
   htmlString = htmlString
      .replace(/<table\b(?![^>]*style=)/g, `<table style="${tableStyle}"`)
      .replace(/<table\b([^>]*?)style="([^"]*?)"/g, (_, attrs, existing) => {
         return `<table${attrs}style="${existing};${tableStyle}"`;
      })
      .replace(/<th\b(?![^>]*style=)/g, `<th style="${thStyle}"`)
      .replace(/<th\b([^>]*?)style="([^"]*?)"/g, (_, attrs, existing) => {
         return `<th${attrs}style="${existing};${thStyle}"`;
      })
      .replace(/<td\b(?![^>]*style=)/g, `<td style="${tdStyle}"`)
      .replace(/<td\b([^>]*?)style="([^"]*?)"/g, (_, attrs, existing) => {
         return `<td${attrs}style="${existing};${tdStyle}"`;
      });

   return htmlString;
};
