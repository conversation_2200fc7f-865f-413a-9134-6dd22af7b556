import { subDays, format } from 'date-fns';
import { KPI } from '../components/interface';
import { UserSocialDetails } from '../../../api/service/onboarding';
import { GoogleAdgroupsData } from '../../../api/service/pulse';
import {
   DaywiseAdKPIsCalculated,
   DaywiseAdsetKPIsCalculated,
   DaywiseTargetingKPIsCalculated,
} from '../../../api/service/pulse/performance-insights/meta-ads';
import {
   BASE_THRESHOLD_INR,
   EXCHANGE_RATES_CACHE_DURATION,
   EXCHANGE_RATES_API_URL,
   FALLBACK_EXCHANGE_RATES,
} from '../../marco/utils/analytics-agent/constants';

export interface Kpis {
   kpi: string;
   kpi_current: number | string;
   kpi_previous: number | string;
}
export const formatValue = (
   value: string | number | null | undefined,
): number => {
   const num = Number(value);
   return Number.isInteger(num) ? num : parseFloat(num.toFixed(2));
};

export const REQUIRED_KPIS_META_ADS = ['clicks', 'impressions', 'reach'];

export const REQUIRED_KPIS_GOOGLE_ADS = [
   'interactions',
   'clicks',
   'impressions',
   // 'spend',
];

export const AdsetInsightsKPIs = {
   age: {
      OUTCOME_SALES: ['spend', 'purchase', 'roas', 'ctr'],
      LINK_CLICKS: ['spend', 'clicks', 'cpc', 'ctr'],
      OUTCOME_LEADS: ['leads', 'cpl', 'ctr'],
   },
   gender: {
      OUTCOME_SALES: ['spend', 'purchase', 'roas', 'ctr'],
      LINK_CLICKS: ['spend', 'clicks', 'cpc', 'ctr'],
      OUTCOME_LEADS: ['leads', 'cpl', 'ctr'],
   },
   placement: {
      OUTCOME_SALES: ['spend', 'purchase', 'roas', 'ctr'],
      LINK_CLICKS: ['spend', 'clicks', 'cpc', 'ctr'],
      OUTCOME_LEADS: ['leads', 'cpl', 'ctr'],
   },
   region: {
      OUTCOME_SALES: ['spend', 'cpc', 'ctr', 'impressions'],
      LINK_CLICKS: ['spend', 'cpc', 'ctr', 'impressions'],
      OUTCOME_LEADS: ['spend', 'cpc', 'ctr', 'impressions'],
   },
   country: {
      OUTCOME_SALES: ['spend', 'purchase', 'roas', 'ctr'],
      LINK_CLICKS: ['spend', 'clicks', 'cpc', 'ctr'],
      OUTCOME_LEADS: ['leads', 'cpl', 'ctr'],
   },
};

export const dropdownDayOptions = [
   { value: '1', label: ' Last 1 Day' },
   { value: '3', label: ' Last 3 Days' },
   { value: '7', label: ' Last 7 Days' },
   { value: '30', label: 'Last 30 Days' },
   { value: '60', label: 'Last 60 Days' },
   { value: '90', label: 'Last 90 Days' },
   { value: '150', label: 'Last 150 Days' },
];

export const getDropdownChannelOptions = (
   connectionDetails: UserSocialDetails[],
) => {
   const dropdownChannelOptions: { value: string; label: string }[] = [];

   const hasFacebookAds = connectionDetails.some(
      (conn) => conn.channel_name === 'facebookads',
   );

   const hasGoogleAds = connectionDetails.some(
      (conn) => conn.channel_name === 'googleads',
   );

   const addOptionIfNotExists = (option: { value: string; label: string }) => {
      const exists = dropdownChannelOptions.some(
         (o) => o.value === option.value,
      );
      if (!exists) {
         dropdownChannelOptions.push(option);
      }
   };

   if (hasFacebookAds) {
      addOptionIfNotExists({ value: 'meta_ads', label: 'Meta Ads' });
   }

   if (hasGoogleAds) {
      addOptionIfNotExists({ value: 'google_ads', label: 'Google Ads' });
   }

   return dropdownChannelOptions;
};

export const dropdownObjectiveOptions = [
   { value: 'OUTCOME_SALES', label: 'Sales' },
   { value: 'LINK_CLICKS', label: 'Traffic' },
   { value: 'OUTCOME_AWARENESS', label: 'Awareness' },
   { value: 'OUTCOME_LEADS', label: 'Leads' },
   { value: 'VIDEO_VIEWS', label: 'Video Views' },
   { value: 'LEAD_GENERATION', label: 'Lead Generation' },
   { value: 'VIDEO', label: 'Video' },
   { value: 'SEARCH', label: 'Search' },
   { value: 'DISPLAY', label: 'Display' },
   { value: 'DEMAND_GEN', label: 'Demand Gen' },
   { value: 'PERFORMANCE_MAX', label: 'Performance Max' },
   { value: 'SHOPPING', label: 'Shopping' },
   { value: 'MULTI_CHANNEL', label: 'Multi Channel' },
];

export const objectiveToMetrics: Record<string, string[]> = {
   OUTCOME_SALES: [
      'roas',
      'cpp',
      'purchase',
      'purchase_rate',
      'spend',
      'clicks',
      'cpc',
      'ctr',
      'frequency',
   ],
   LINK_CLICKS: ['clicks', 'ctr', 'cpc', 'impressions', 'spend', 'frequency'],
   OUTCOME_AWARENESS: ['reach', 'impressions', 'ctr', 'spend', 'frequency'],
   OUTCOME_LEADS: [
      'leads',
      'leads_conversion_rate',
      'clicks',
      'cpl',
      'ctr',
      // 'unique_ctr',
      'reach',
      'impressions',
      'cpc',
      'spend',
   ],
   VIDEO_VIEWS: [
      'impressions',
      'video_watch_100_percent',
      'video_watch_95_percent',
      'video_watch_75_percent',
      'video_watch_50_percent',
      'ctr',
      'reach',
      'cpm',
      'spend',
   ],
   LEAD_GENERATION: [
      'leads',
      'leads_conversion_rate',
      'clicks',
      'cpl',
      'ctr',
      // 'unique_ctr',
      'reach',
      'impressions',
      'cpc',
      'spend',
   ],
   VIDEO: ['ctr', 'impressions', 'conversions', 'cpc'],
   SEARCH: [
      'ctr',
      'cpc',
      'conversions',
      'conversion_rate',
      'cpa',
      'impressions',
      'spend',
   ],
   DISPLAY: [
      'impressions',
      'ctr',
      'conversion_rate',
      'conversions',
      'cpa',
      'spend',
   ],
   DEMAND_GEN: [
      'ctr',
      'cpc',
      'conversions',
      'cac',
      'impressions',
      'cpm',
      'spend',
   ],
   PERFORMANCE_MAX: [
      'conversions',
      'conversion_rate',
      'cpa',
      'impressions',
      'cpc',
      'spend',
   ],
   SHOPPING: [
      'ctr',
      'cpc',
      'conversions',
      'conversion_rate',
      'cpa',
      'impressions',
      'spend',
   ],
   MULTI_CHANNEL: [
      'interactions',
      'roas',
      'video_views',
      'cpa',
      'clicks',
      'ctr',
      'impressions',
      'conversions_value',
      'spend',
      'cpm',
      'conversion_rate',
      'conversions',
      'cpc',
   ],
};

export const withoutBaseMetrics: Record<string, string[]> = {
   OUTCOME_SALES: ['roas', 'cpp', 'purchase_rate', 'cpc', 'ctr', 'frequency'],
   LINK_CLICKS: ['ctr', 'cpc', 'frequency'],
   OUTCOME_AWARENESS: ['ctr', 'frequency'],
   OUTCOME_LEADS: ['leads_conversion_rate', 'cpl', 'ctr', 'cpc'],
   VIDEO_VIEWS: ['ctr', 'cpm'],
   LEAD_GENERATION: [
      'leads',
      'leads_conversion_rate',
      'clicks',
      'cpl',
      'ctr',
      // 'unique_ctr',
      'reach',
      'impressions',
      'cpc',
      'spend',
   ],
};

export const cap = (value: string) => {
   return value
      ?.replace(/_/g, ' ')
      ?.replace(/\b\w/g, (char) => char.toUpperCase());
};

export const toUpperCase = (value: string) =>
   value.replace(/_/g, ' ').toUpperCase();

export const toLowerCase = (value: string) =>
   value.replace(/_/g, ' ').toLowerCase();

export const toShowCurrency = (kpi: string, currency: string) => {
   if (kpi === 'cpp' || kpi === 'cpc' || kpi === 'spend' || kpi === 'cpa') {
      const currencyCode = currency.replace(/[[\]']+/g, '');
      const formattedNumber = new Intl.NumberFormat('en-US', {
         style: 'currency',
         currency: currencyCode,
      }).format(0);
      return formattedNumber.replace(/[0-9.,]/g, '').trim();
   } else return null;
};

let exchangeRatesCache: { [key: string]: number } | null = null;
let cacheTimestamp: number = 0;

const fetchExchangeRates = async (): Promise<{ [key: string]: number }> => {
   try {
      const response = await fetch(EXCHANGE_RATES_API_URL);
      const data = (await response.json()) as {
         rates?: { [key: string]: number };
      };

      if (data.rates) {
         const inrRates: { [key: string]: number } = { ...data.rates };
         inrRates['INR'] = 1.0;

         return inrRates;
      }
      throw new Error('Invalid API response');
   } catch (error) {
      console.warn(
         'Failed to fetch exchange rates, using fallback rates:',
         error,
      );

      return FALLBACK_EXCHANGE_RATES;
   }
};

const getExchangeRates = async (): Promise<{ [key: string]: number }> => {
   const now = Date.now();

   if (
      exchangeRatesCache &&
      now - cacheTimestamp < EXCHANGE_RATES_CACHE_DURATION
   ) {
      return exchangeRatesCache;
   }

   const rates = await fetchExchangeRates();
   exchangeRatesCache = rates;
   cacheTimestamp = now;

   return rates;
};

export const getCurrencyThreshold = async (
   currency: string,
): Promise<string> => {
   const cleanCurrency = currency.replace(/[[\]']+/g, '').toUpperCase();

   try {
      const rates = await getExchangeRates();

      const rate = rates[cleanCurrency] || 1.0;
      const convertedAmount = BASE_THRESHOLD_INR * rate;

      const formattedThreshold = new Intl.NumberFormat('en-US', {
         maximumFractionDigits: 1,
      }).format(convertedAmount);

      return `${formattedThreshold} ${cleanCurrency}`;
   } catch (error) {
      console.error('Error getting currency threshold:', error);

      return `${BASE_THRESHOLD_INR} INR`;
   }
};
export const formatDateRange = (dateRange: string): string => {
   const [, startMonth, startDay, , endMonth, endDay] = dateRange.split('-');

   const months = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec',
   ];

   const startFormatted = `${parseInt(startDay)} ${months[parseInt(startMonth) - 1]}`;
   const endFormatted = `${parseInt(endDay)} ${months[parseInt(endMonth) - 1]}`;

   return `${startFormatted} - ${endFormatted}`;
};

export const removeColumnsWithNoKPIValue = (data: KPI[]): KPI[] | [] => {
   return data?.filter((kpi) => !Number.isNaN(kpi.kpi_value));
};

export const differenceInDays = (date1: string, date2: string) => {
   const normalizedDate1 = new Date(new Date(date1).setHours(0, 0, 0, 0));
   const normalizedDate2 = new Date(new Date(date2).setHours(23, 59, 59, 999));
   const diffTime = Math.abs(
      normalizedDate2.getTime() - normalizedDate1.getTime(),
   );
   const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
   return diffDays;
};

// --------------------- Google Ads

export const extractLinesFromRecommendation = (
   recommendationString: string,
): string[] | null => {
   try {
      const cleanedString: string = recommendationString.replace(
         /```json\n|```/g,
         '',
      );
      const parsedJson = JSON.parse(cleanedString) as Record<string, string>;

      return Object.values(parsedJson);
   } catch (error) {
      console.error('Error parsing recommendation', error);
      return null;
   }
};

// Helper function to truncate text
export const truncateText = (
   text: string,
   isExpanded: boolean,
   length: number,
) => {
   return isExpanded
      ? text
      : text?.length > length
        ? `${text?.substring(0, length)}... `
        : text;
};

export const StatusTypes = {
   ACTIVE: 'ACTIVE',
   ENABLED: 'ENABLED',
   PAUSED: 'PAUSED',
   REMOVED: 'REMOVED',
};

export const startEndDate = (
   days: string,
): { payloadStartDate: string; payloadEndDate: string } => {
   const today = new Date();
   const endDate = new Date(today);
   endDate.setDate(today.getDate() - 1);
   const daysNumber = parseInt(days, 10);
   const startDate = subDays(endDate, daysNumber - 1);
   const formattedStartDate = format(startDate, 'yyyy-MM-dd');
   const formattedEndDate = format(endDate, 'yyyy-MM-dd');
   return {
      payloadStartDate: formattedStartDate,
      payloadEndDate: formattedEndDate,
   };
};

export const sortAdGroupsByNestedKPI = (
   ads: GoogleAdgroupsData[],
   targetKPI: string,
   order = 'asc',
) => {
   return [...ads].sort((a, b) => {
      const aKPI =
         a.kpis.find((k) => k.kpi === targetKPI)?.kpi_current ?? -Infinity;
      const bKPI =
         b.kpis.find((k) => k.kpi === targetKPI)?.kpi_current ?? -Infinity;

      const aVal = aKPI === null ? -Infinity : Number(aKPI);
      const bVal = bKPI === null ? -Infinity : Number(bKPI);

      return order === 'asc' ? aVal - bVal : bVal - aVal;
   });
};

export const sortAdSetsByNestedKPI = (
   adsets: DaywiseAdsetKPIsCalculated[],
   targetKPI: string,
   order = 'asc',
   metricOrder: string[] = [],
) => {
   const rearrangedAdsets =
      adsets?.map((adset) => ({
         ...adset,
         kpis: adset.kpis.slice().sort((a, b) => {
            const indexA = metricOrder.indexOf(a.kpi_name);
            const indexB = metricOrder.indexOf(b.kpi_name);
            return (
               (indexA === -1 ? Infinity : indexA) -
               (indexB === -1 ? Infinity : indexB)
            );
         }),
      })) || [];

   return [...rearrangedAdsets].sort((a, b) => {
      const aKPI =
         a.kpis.find((k) => k.kpi_name === targetKPI)?.kpi_current ?? -Infinity;
      const bKPI =
         b.kpis.find((k) => k.kpi_name === targetKPI)?.kpi_current ?? -Infinity;

      const aVal = aKPI === null ? -Infinity : Number(aKPI);
      const bVal = bKPI === null ? -Infinity : Number(bKPI);

      return order === 'asc' ? aVal - bVal : bVal - aVal;
   });
};

export const sortAdsByNestedKPI = (
   ads: DaywiseAdKPIsCalculated[],
   targetKPI: string,
   order = 'asc',
) => {
   return [...ads].sort((a, b) => {
      const aKPI =
         a.kpis.find((k) => k.kpi_name === targetKPI)?.kpi_current ?? -Infinity;
      const bKPI =
         b.kpis.find((k) => k.kpi_name === targetKPI)?.kpi_current ?? -Infinity;

      const aVal = aKPI === null ? -Infinity : Number(aKPI);
      const bVal = bKPI === null ? -Infinity : Number(bKPI);

      return order === 'asc' ? aVal - bVal : bVal - aVal;
   });
};

export const DEFAULT_SORT_BY_OBJECTIVE = {
   OUTCOME_SALES: 'roas',
   LINK_CLICKS: 'clicks',
   OUTCOME_LEADS: 'leads',
   LEAD_GENERATION: 'leads',
   VIDEO_VIEWS: 'impressions',
};

export const getDateRange = (
   dateRange: { start: string; end: string },
   prevRange: { start: string; end: string },
) => {
   const MS_IN_DAY = 24 * 60 * 60 * 1000;

   const parseUTCDate = (str: string): Date => {
      const [year, month, day] = str.split('T')[0].split('-').map(Number);
      return new Date(Date.UTC(year, month - 1, day));
   };

   const format = (d: Date) =>
      `${d.getUTCFullYear()}-${String(d.getUTCMonth() + 1).padStart(2, '0')}-${String(d.getUTCDate()).padStart(2, '0')}`;

   const startDateObj = parseUTCDate(dateRange.start); // 👈 No +MS_IN_DAY
   const endDateObj = parseUTCDate(dateRange.end);
   const prevStartDateObj = parseUTCDate(prevRange.start);
   const prevEndDateObj = parseUTCDate(prevRange.end);

   const days =
      Math.floor((endDateObj.getTime() - startDateObj.getTime()) / MS_IN_DAY) +
      1;

   return {
      start_date: format(startDateObj),
      end_date: format(endDateObj),
      prev_start_date: format(prevStartDateObj),
      prev_end_date: format(prevEndDateObj),
      days,
   };
};

export const getTop5BySpend = (
   data: DaywiseTargetingKPIsCalculated[],
): DaywiseTargetingKPIsCalculated[] => {
   return data
      .map((item) => {
         const spendKPI = item.kpis.find(
            (kpi) => kpi.kpi_name === 'spend' && kpi.kpi_value !== 'NaN',
         );
         return {
            ...item,
            spend:
               typeof spendKPI?.kpi_value === 'number' ? spendKPI.kpi_value : 0,
         };
      })
      .sort((a, b) => b.spend - a.spend)
      .slice(0, 5)
      .map(({ ...rest }) => rest);
};
