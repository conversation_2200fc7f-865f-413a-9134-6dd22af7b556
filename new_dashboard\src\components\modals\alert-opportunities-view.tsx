import { useAppDispatch, useAppSelector } from '@/store/store';
import ModalWrapper from './modal-wrapper';
import { Flex, Text, Box, useColorMode } from '@chakra-ui/react';
import { GoAlert } from 'react-icons/go';
import { IoIosTrendingUp } from 'react-icons/io';
import { alertsAndOpportunities } from '@/pages/dashboard/utils/interface';
import { useNavigate } from 'react-router-dom';
import { FaUpRightFromSquare } from 'react-icons/fa6';
import { useState } from 'react';
import { closeModal } from '@/store/reducer/modal-reducer';
import {
   setCurrentSessionID,
   setCurrentMode,
   setKpiPrompts,
   setKpiMetadata,
   clearKpiMetadata,
} from '@/store/reducer/analytics-agent-reducer';
import { format } from 'date-fns';
import AlertCategoryImages from '@/pages/dashboard/utils/alert-images';


function AlertsOpportunitiesViewModal() {
   const { colorMode } = useColorMode();
   const navigate = useNavigate();
   const dispatch = useAppDispatch();
   const currentModal = useAppSelector((state) => state.modal);
   const [hoveredIdx, setHoveredIdx] = useState<number | null>(null);
const { dateRange, prevRange } = useAppSelector((state) => state.kpi);
const currentPeriod = `${format(new Date(dateRange.start), 'MMM d')} - ${format(new Date(dateRange.end), 'MMM d')}`;
  const previousPeriod = `${format(new Date(prevRange.start), 'MMM d')} - ${format(new Date(prevRange.end), 'MMM d')}`;

   const allAlertsAndOpportunities = (currentModal.payload?.modalProps
      ?.allAlertsAndOpportunities || {}) as Record<
      string,
      alertsAndOpportunities
   >;

    const handleNavigate = (alert: alertsAndOpportunities) => {
     const { kpiName, direction, percentage } = alert;
   
     const buildAgentPrompt = () => {
       return `
   You are the AI CMO analyzing ad performance.
   
   The KPI **${kpiName}** has ${direction} by **${percentage}%** when comparing the period **${currentPeriod}** vs **${previousPeriod}**.
   
   Please analyze:
   1. What are the potential root causes for this ${direction} in ${kpiName}?
   2. Which campaigns, ad sets, or audiences contributed most to the change?
   3. Identify whether the shift is due to budget allocation, creative performance, targeting, or platform delivery issues.
   4. Provide actionable insights and recommendations to improve ${kpiName} in the next period.
       `;
     };
   
     dispatch(setCurrentSessionID(''));
     dispatch(setCurrentMode('data-analyst'));
     dispatch(closeModal());
     dispatch(
       setKpiPrompts({
         displayPrompt:`Analyze alert:${ alert.message}`,  
         aiPrompt: buildAgentPrompt(),
       })
     );
   
     navigate('/marco/analytics-agent');
   };

   return (
      <ModalWrapper
         heading='Alerts & Opportunities'
         overlayBgcolor='#2424241c'
         parentClassName='alerts-view'
      >
         <Flex
            direction='column'
            gap={3}
            maxHeight='400px'
            overflowY='auto'
            p={2}
         >
            {Object.values(allAlertsAndOpportunities).map((alert, idx) => (
               <Flex
                  align='center'
                  gap={3}
                  background={colorMode === 'dark' ? 'gray.700' : '#f9f9f9'}
                  borderRadius='md'
                  p={3}
                  boxShadow={
                     colorMode === 'dark'
                        ? '1px 1px 10px 1px #00000033'
                        : '1px 1px 10px 1px #cccccc33'
                  }
                  key={idx}
                  onMouseEnter={() => setHoveredIdx(idx)}
                  onMouseLeave={() => setHoveredIdx(null)}
               >
                  {alert.isPositive ? (
                     <Flex
                        align='center'
                        justify='center'
                        bg='#e8f8ec'
                        color='#2b8a3e'
                        borderRadius='full'
                        boxSize='42px'
                     >
                        <IoIosTrendingUp size={22} />
                     </Flex>
                  ) : (
                     <Flex
                        align='center'
                        justify='center'
                        bg='#ffeaea'
                        color='#e03131'
                        borderRadius='full'
                        boxSize='42px'
                        pb='1px'
                     >
                        <GoAlert size={22} />
                     </Flex>
                  )}
                  <Flex flex='1' align='center' gap={3}>
        
        <AlertCategoryImages category={alert.category}  />

        
        <Box flex='1'>
          <Flex gap={2} align='center'>
            <Text fontSize={13} fontWeight={600}>
              {alert.kpiName}
            </Text>
            {hoveredIdx === idx && (
              <FaUpRightFromSquare
                size={13}
                style={{ cursor: 'pointer' }}
                onClick={() => handleNavigate(alert)}
                color='#1D4ED8'
              />
            )}
          </Flex>

          <Text
            fontSize={12}
            color={colorMode === 'dark' ? 'gray.300' : 'gray.600'}
          >
            {alert.message}
          </Text>
        </Box>
      </Flex>
               </Flex>
            ))}
         </Flex>
      </ModalWrapper>
   );
}

export default AlertsOpportunitiesViewModal;
