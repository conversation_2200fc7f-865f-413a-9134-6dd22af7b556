import { Tooltip } from '@chakra-ui/react';
import { TooltipContentProps } from '../utils/interface';

function TooltipContent(props: TooltipContentProps) {
   const { children, kpi, category, label, hasArrow, placement } = props;
   let finalLabel: string | React.ReactNode = label || '';
   if (kpi) {
      switch (kpi) {
         case 'return_order_items_count':
            finalLabel = (
               <div>
                  <p>
                     The total number of individual items returned across all
                     orders that were not exchanged.
                  </p>
               </div>
            );
            break;
         case 'amazon_units_shipped':
            finalLabel = (
               <div>
                  <p>The total number of units that have been shipped.</p>
               </div>
            );
            break;
         case 'amazon_units_ordered':
            finalLabel = (
               <div>
                  <p>
                     The total number of individual items purchased across all
                     orders.
                  </p>
               </div>
            );
            break;
         case 'amazon_shipping_revenue':
            finalLabel = (
               <div>
                  <p>
                     Total product revenue generated from units that have been
                     shipped.
                  </p>
               </div>
            );
            break;
         case 'amazon_shipped_cogs_amount':
            finalLabel = (
               <div>
                  <p>
                     Total cost of goods sold (COGS) for units that have been
                     shipped.
                  </p>
               </div>
            );
            break;
         case 'amazon_units_cancelled':
            finalLabel = (
               <div>
                  <p>Total number of units cancelled.</p>
               </div>
            );
            break;
         case 'average_order_value':
            finalLabel = (
               <div>
                  <p>
                     The average amount spent per order, calculated as total
                     revenue divided by the number of orders.
                  </p>
                  <p>
                     <strong>Formula:</strong> SUM(Order Revenue - Shipping
                     Charges - Taxes) ÷ Total Orders
                  </p>
               </div>
            );
            break;
         case 'amazon_average_order_value':
            finalLabel = (
               <div>
                  <p>
                     Average Order Value (AOV) is a metric that represents the
                     average revenue generated per order. It is calculated by
                     dividing the total revenue from valid orders by the number
                     of unique orders.
                  </p>
                  <p>
                     <strong>Formula:</strong> AOV = Sum of Order Amount /
                     Number of Orders
                  </p>
               </div>
            );
            break;
         case 'avg_pages_per_session':
            finalLabel = (
               <div>
                  <p>
                     The average number of pages viewed per session on your
                     website.
                  </p>
                  <p>
                     <strong>Formula:</strong> SUM(screen_page_views) /
                     SUM(sessions)
                  </p>
               </div>
            );
            break;
         case 'net_shipping_charges':
            finalLabel = <p>Total shipping cost incurred across all sales.</p>;
            break;
         case 'amazon_total_page_views':
            finalLabel = <p>The total number of pages viewed per session.</p>;
            break;
         case 'avg_session_duration':
            finalLabel = (
               <div>
                  <p>
                     The average length of time users spend on your website in a
                     single session.
                  </p>
                  <p>
                     <strong>Formula:</strong> SUM(user_engagement_duration) /
                     SUM(sessions)
                  </p>
               </div>
            );
            break;
         case 'google_cost_per_conversion':
            finalLabel = (
               <div>
                  <p>
                     Cost Per Acquisition (CPA) is a metric that calculates the
                     average cost of acquiring a single conversion through
                     Google Ads. It measures the efficiency of your ad spend by
                     dividing the total cost by the total number of conversions.
                  </p>
                  <p>
                     <strong>Formula:</strong>CPA =Total Ad Cost/Total
                     Conversions
                  </p>
               </div>
            );
            break;
         case 'amazon_canceled_orders':
            finalLabel = (
               <div>
                  <p>
                     Canceled Orders is a metric that measures the total number
                     of orders that were canceled.
                  </p>
                  <p>
                     <strong>Formula:</strong>Canceled Orders = Count of Orders
                     with "Canceled" Status
                  </p>
               </div>
            );
            break;
         case 'conversion_rate':
            finalLabel = (
               <div>
                  <p>The percentage of sessions that resulted in a purchase.</p>
                  <p>
                     <strong>
                        Conversion Rate = (Total Conversions / Total Sessions) *
                        100
                     </strong>
                  </p>
               </div>
            );
            break;
         case 'amazon_ads_conversion_rate':
         case 'amazon_conversion_rate':
            finalLabel = (
               <div>
                  <p>
                     <strong>Conversion Rate</strong>
                  </p>
                  <p>
                     The conversion rate is a metric that measures the
                     percentage of users who complete a desired action (e.g.,
                     making a purchase) out of the total number of visitors. It
                     is calculated as:
                  </p>
                  <p>
                     <strong>
                        Conversion Rate = (Total Conversions / Total Clicks) *
                        100
                     </strong>
                  </p>
                  <p>
                     This data helps you understand the effectiveness of your
                     marketing and user experience efforts in driving user
                     actions.
                  </p>
               </div>
            );
            break;
         case 'google_conversion_rate':
            finalLabel = (
               <div>
                  <p>
                     <strong>Conversion Rate</strong>
                  </p>
                  <p>
                     The conversion rate is a metric that measures the
                     percentage of users who complete a desired action (e.g.,
                     making a purchase) out of the total number of visitors. It
                     is calculated as:
                  </p>
                  <p>
                     <strong>
                        Conversion Rate = (Total Conversions / Total
                        Interactions or Clicks) * 100
                     </strong>
                  </p>
               </div>
            );
            break;
         case 'lead_conversion_rate':
            finalLabel = (
               <div>
                  <p>
                     Percentage of ad clicks that resulted in leads from Meta
                     Ads.
                  </p>
                  <p>
                     <strong>
                        Lead Conversion Rate=Total Leads/Total Clicks×100
                     </strong>
                  </p>
               </div>
            );
            break;
         case 'purchase_rate':
            finalLabel = (
               <div>
                  <p>
                     Percentage of ad clicks on Meta that resulted in purchases.
                  </p>
                  <p>
                     <strong>
                        Purchase Rate=Total Purchases/Total Clicks×100
                     </strong>
                  </p>
               </div>
            );
            break;
         case 'abandoned_checkout':
            finalLabel = (
               <div>
                  <p>
                     <strong>Abandoned Checkout</strong>
                  </p>
                  <p>
                     The number of checkout sessions where customers added items
                     to their cart, entered the checkout process, but did not
                     complete the purchase.
                  </p>
               </div>
            );
            break;
         case 'cost_per_session':
            finalLabel = (
               <div>
                  <p>
                     <strong>Cost Per Session</strong>
                  </p>
                  <p>
                     Cost per session is a metric that measures the average
                     amount spent to drive a single session (visit) on your
                     website. It is calculated as:
                  </p>
                  <p>
                     <strong>
                        Cost Per Session = Total Advertising Spend / Total
                        Number of Sessions
                     </strong>
                  </p>
                  <p>
                     This metric helps you understand the efficiency of your
                     advertising spend in driving traffic to your website.
                  </p>
               </div>
            );
            break;
         case 'bounce_rate':
            finalLabel = (
               <div>
                  <p>
                     The percentage of sessions in which users did not actively
                     engage with the content. A higher bounce rate indicates
                     that more sessions ended without meaningful interactions.
                  </p>
                  <p>
                     <strong>Formula:</strong> Bounce Rate = 1 -
                     (SUM(engaged_sessions) / SUM(sessions)) * 100
                  </p>
               </div>
            );
            break;
         case 'engagement_rate':
            finalLabel = (
               <div>
                  <p>
                     The percentage of sessions in which users were actively
                     engaged with the content.
                  </p>
                  <p>
                     <strong>Formula:</strong> (SUM(engaged_sessions) /
                     SUM(sessions)) * 100
                  </p>
               </div>
            );
            break;
         case 'user_engagement_duration':
            finalLabel = (
               <div>
                  <p>
                     Total time (in seconds) users spent actively engaging with
                     your website or app. This includes time when the page was
                     in focus and the user was interacting.
                  </p>
                  {/* <p>
                     <strong>Formula:</strong> Bounce Rate = 1 -
                     (SUM(engaged_sessions) / SUM(sessions)) * 100
                  </p> */}
               </div>
            );
            break;
         case 'engaged_sessions':
            finalLabel = (
               <div>
                  <p>
                     Number of sessions that lasted longer than 10 seconds, had
                     a conversion event, or had at least 2 pageviews/screens.
                  </p>
                  {/* <p>
                     <strong>Formula:</strong> Bounce Rate = 1 -
                     (SUM(engaged_sessions) / SUM(sessions)) * 100
                  </p> */}
               </div>
            );
            break;
         case 'screen_page_views':
            finalLabel = (
               <div>
                  <p>
                     Total number of pages or screens viewed by users. This
                     counts all page_view events across all sessions.
                  </p>
                  {/* <p>
                     <strong>Formula:</strong> Bounce Rate = 1 -
                     (SUM(engaged_sessions) / SUM(sessions)) * 100
                  </p> */}
               </div>
            );
            break;
         case 'cpc':
         case 'google_cpc':
         case 'amazon_cpc':
            finalLabel = (
               <div>
                  <h3>Cost Per Click (CPC)</h3>
                  <p>CPC is the average amount spent per click.</p>
                  <p>
                     <strong>Formula:</strong> CPC = Total Spend / Total Clicks
                  </p>
               </div>
            );
            break;
         case 'cpm':
            finalLabel = (
               <div>
                  <p>The cost per thousand impressions of your Meta ads.</p>
                  <p>
                     <strong>Formula:</strong> CPM = (Total Spend / Total
                     Impressions) * 1000
                  </p>
               </div>
            );
            break;
         case 'google_interactions':
            finalLabel = (
               <div>
                  <p>
                     Total user engagements (like clicks, views, or other
                     actions) recorded from ad campaigns.
                  </p>
                  <p>
                     <strong>Formula:</strong> Interactions = Sum of
                     Interactions from Ads
                  </p>
               </div>
            );
            break;
         case 'amazon_vcpm':
            finalLabel = (
               <div>
                  <h3>vCPM (Viewable CPM)</h3>
                  <p>
                     Where vCPM measures the cost per thousand viewable
                     impressions.
                  </p>
                  <p>
                     <strong>Formula:</strong> CPM = (Total Spend / Total
                     Viewable Impressions) * 1000
                  </p>
               </div>
            );
            break;
         case 'cpv':
            finalLabel = (
               <div>
                  <p>The cost per view of your video ads.</p>
                  <p>
                     <strong>Formula:</strong> CPV = (Total Ads Spend / Total
                     Video Views)
                  </p>
               </div>
            );
            break;
         case 'vtr':
            finalLabel = (
               <div>
                  <p>
                     The percentage of users who watched your video ad to
                     completion after being exposed to it.
                  </p>
                  <p>
                     <strong>Formula:</strong> VTR = (Total Video Completions /
                     Impressions) * 100
                  </p>
               </div>
            );
            break;
         case 'cost_per_lead':
            finalLabel = (
               <div>
                  <p>
                     The cost incurred to acquire a lead, i.e., a potential
                     customer who has expressed interest in your product or
                     service.
                  </p>
                  <p>
                     <strong>Formula:</strong> CPL = (Total Ad Spend / Total
                     Leads Generated)
                  </p>
               </div>
            );
            break;
         case 'leads':
            finalLabel = (
               <div>
                  <p>
                     The number of potential customers who have expressed
                     interest in your product or service by providing their
                     contact information.
                  </p>
                  <p>
                     <strong>Formula:</strong> Leads = Number of People Who
                     Provided Contact Information
                  </p>
               </div>
            );
            break;
         case 'video_view':
            finalLabel = (
               <div>
                  <p>
                     The total number of times users have watched your video ad.
                  </p>
                  <p>
                     <strong>Formula:</strong> Video Views = Number of Users Who
                     Viewed the Video
                  </p>
               </div>
            );
            break;
         case '25_percent_video_views':
            finalLabel = (
               <div>
                  <p>
                     The number of users who watched at least 25% of your video.
                  </p>
                  <p>
                     <strong>Formula:</strong> 25% Video Views = Number of Users
                     Who Watched 25% of the Video
                  </p>
               </div>
            );
            break;
         case '75_percent_video_views':
            finalLabel = (
               <div>
                  <p>
                     The number of users who watched at least 75% of your video.
                  </p>
                  <p>
                     <strong>Formula:</strong> 75% Video Views = Number of Users
                     Who Watched 75% of the Video
                  </p>
               </div>
            );
            break;
         case '95_percent_video_views':
            finalLabel = (
               <div>
                  <p>
                     The number of users who watched at least 95% of your video.
                  </p>
                  <p>
                     <strong>Formula:</strong> 95% Video Views = Number of Users
                     Who Watched 95% of the Video
                  </p>
               </div>
            );
            break;
         case '50_percent_video_views':
            finalLabel = (
               <div>
                  <p>
                     The number of users who watched at least 50% of your video.
                  </p>
                  <p>
                     <strong>Formula:</strong> 50% Video Views = Number of Users
                     Who Watched 50% of the Video
                  </p>
               </div>
            );
            break;
         case '100_percent_video_views':
            finalLabel = (
               <div>
                  <p>
                     The number of users who watched your video all the way to
                     the end.
                  </p>
                  <p>
                     <strong>Formula:</strong> 100% Video Views = Number of
                     Users Who Watched the Video to the End
                  </p>
               </div>
            );
            break;
         case 'cpp':
            finalLabel = (
               <div>
                  <p>
                     The cost per purchase made through your Meta ads, also
                     known as cost per acquisition (CPA).
                  </p>
                  <p>
                     <strong>Formula:</strong> CPP = Total Spend / Total
                     Purchases
                  </p>
               </div>
            );
            break;
         case 'ctr':
            finalLabel = (
               <div>
                  <p>
                     The percentage of people who click on your Meta ad after
                     seeing it.
                  </p>
                  <p>
                     <strong>Formula:</strong> CTR = (Total Clicks / Total
                     Impressions) * 100
                  </p>
               </div>
            );
            break;
         case 'amazon_ctr':
            finalLabel = (
               <div>
                  <h3>Click-Through Rate (CTR)</h3>
                  <p>
                     Where CTR measures the percentage of impressions that
                     resulted in clicks.
                  </p>
                  <p>
                     <strong>Formula:</strong> CTR = (Total Clicks / Total
                     Impressions) * 100
                  </p>
               </div>
            );
            break;
         case 'google_cpm':
            finalLabel = (
               <div>
                  <p>The cost per thousand impressions of your Google ads.</p>
                  <p>
                     <strong>Formula:</strong> CPM = (Total Ad Spend / Total
                     Impressions) * 1000
                  </p>
               </div>
            );
            break;
         case 'google_cpa':
            finalLabel = (
               <div>
                  <h3>Google CPA</h3>
                  <p>
                     Cost Per Acquisition (CPA) is a metric that calculates the
                     average cost of acquiring a single conversion through
                     Google Ads. It measures the efficiency of your ad spend by
                     dividing the total cost by the total number of conversions.
                  </p>
                  <p>
                     <strong>Formula:</strong> CPA = Total Ad Spend / Total
                     Conversions
                  </p>
               </div>
            );
            break;
         case 'google_cpp':
            finalLabel = (
               <div>
                  <p>
                     The cost per purchase made through your Google ads, also
                     known as cost per acquisition (CPA).
                  </p>
                  <p>
                     <strong>Formula:</strong> CPP = Total Spend / Total
                     Purchases
                  </p>
               </div>
            );
            break;
         case 'google_ctr':
            finalLabel = (
               <div>
                  <p>
                     The percentage of people who click on your Google ad after
                     seeing it.
                  </p>
                  <p>
                     <strong>Formula:</strong> CTR = (Total Clicks / Total
                     Impressions) * 100
                  </p>
               </div>
            );
            break;
         case 'new_customers':
            finalLabel = (
               <p>
                  The number of customers who made their first purchase during a
                  specific period.
               </p>
            );
            break;
         case 'amazon_new_customers':
            finalLabel = (
               <p>
                  The number of customers who made their first purchase during a
                  specific period.
               </p>
            );
            break;
         case 'amazon_ads_gross_sales':
            finalLabel = (
               <div>
                  <p>
                     The total revenue generated from all sales of goods and
                     services before any deductions.
                  </p>
                  <p>
                     <strong>Formula:</strong> Gross Sales = SUM(Sales Amount
                     from All Orders)
                  </p>
               </div>
            );
            break;
         case 'amazon_net_sales':
            finalLabel = (
               <div>
                  <p>
                     Net sales refer to the total revenue from sales after
                     subtracting discounts, returns, and shipping charges,
                     calculated for a specific period.
                  </p>
                  <p>
                     <strong>Formula:</strong> Gross Sales - Discount - Returns
                  </p>
               </div>
            );
            break;
         case 'net_sales':
            finalLabel = (
               <div>
                  <p>
                     The total revenue from customer purchases after subtracting
                     returns and discounts from gross sales. It does not include
                     taxes or shipping charges.
                  </p>
                  <p>
                     <strong>Formula:</strong> Net Sales = Gross Sales − Returns
                     − Discounts
                  </p>
               </div>
            );
            break;
         case 'total_sales':
            finalLabel = (
               <div>
                  <p>
                     The total revenue from customer purchases after deducting
                     returns and discounts, and including taxes and shipping
                     charges. It represents the full amount received from
                     customers.
                  </p>
                  <p>
                     <strong>Formula:</strong> Total Sales = Net Sales +
                     Shipping Charges + Taxes
                  </p>
               </div>
            );
            break;
         case 'returns':
            finalLabel = (
               <div>
                  <p>The total value of products returned by customers.</p>
               </div>
            );
            break;
         case 'amazon_acos':
            finalLabel = (
               <div>
                  <h3>Advertising Cost of Sales (ACoS)</h3>
                  <p>
                     Where ACoS represents the percentage of ad spend in
                     relation to total revenue.
                  </p>
                  <p>
                     <strong>Formula:</strong> (Total Spend ÷ Total Revenue) X
                     100
                  </p>
               </div>
            );
            break;
         case 'amazon_net_sale':
            finalLabel = (
               <div>
                  <p>
                     Net sales represent the actual revenue a company earns
                     after accounting for various deductions from gross sales.
                  </p>
                  <p>
                     <strong>Formula:</strong> Net Sales = Gross Sales -
                     Discounts - Returns + Shipping Charges + Taxes
                  </p>
               </div>
            );
            break;
         case 'amazon_order_revenue':
            finalLabel = (
               <div>
                  <p>
                     The total revenue after adjustments such as discounts,
                     shipping, fees, and taxes (but before refunds).
                  </p>
                  <p>
                     <strong>Formula:</strong> Order Revenue = Gross Sales -
                     Discounts + Shipping + Tax
                  </p>
               </div>
            );
            break;
         case 'blended_gross_sales':
            finalLabel = (
               <div>
                  <p>
                     The total sales revenue from all sales channels before
                     deducting any discounts, returns, taxes, or other
                     adjustments.
                  </p>
               </div>
            );
            break;
         case 'amazon_returns':
            finalLabel = <p>Amazon Returns.</p>;
            break;
         case 'new_users':
            finalLabel = (
               <p>
                  The number of users who visited your website for the first
                  time during a specific period.
               </p>
            );
            break;
         case 'returning_customers':
            finalLabel = (
               <p>
                  The number of customers who have made more than one purchase.
               </p>
            );
            break;
         case 'amazon_return_customers':
            finalLabel = (
               <p>
                  The number of customers who have made more than one purchase.
               </p>
            );
            break;
         case 'roas':
         case 'amazon_roas':
            finalLabel = (
               <div>
                  <p>
                     A measure of the revenue generated for every dollar spent
                     on advertising.
                  </p>
                  <p>
                     <strong>Formula:</strong> ROAS = Total Revenue / Total Ad
                     Spend
                  </p>
               </div>
            );
            break;
         case 'google_roas':
            finalLabel = (
               <div>
                  <p>
                     A measure of the revenue generated for every dollar spent
                     on advertising.
                  </p>
                  <p>
                     <strong>Formula:</strong> ROAS = Total Conversion Value /
                     Total Ad Spend
                  </p>
               </div>
            );
            break;
         case 'total_clicks':
            finalLabel = (
               <p>
                  The total number of clicks on your Meta ads received on a
                  specific date.
               </p>
            );
            break;
         case 'google_total_clicks':
            finalLabel = (
               <p>
                  The total number of clicks on your Google ads received on a
                  specific date.
               </p>
            );
            break;
         case 'amazon_total_clicks':
            finalLabel = (
               <p>
                  Where Total Clicks represent the total number of clicks
                  received on a specific date.
               </p>
            );
            break;
         case 'total_impressions':
            finalLabel = (
               <p>The total number of times your Meta ads were displayed.</p>
            );
            break;
         case 'google_total_impressions':
            finalLabel = (
               <p>The total number of times your Google ads were displayed.</p>
            );
            break;
         case 'amazon_total_impressions':
            finalLabel = (
               <p>The total number of times your Amazon ads were displayed.</p>
            );
            break;
         case 'google_total_conversions':
            finalLabel = <p>The total number of conversions.</p>;
            break;
         case 'total_orders':
            finalLabel = (
               <p>
                  The total number of orders placed during a specific period.
               </p>
            );
            break;
         case 'amazon_total_orders':
            finalLabel = (
               <p>
                  The total number of orders placed excluding canceled orders.
               </p>
            );
            break;
         case 'amazon_return_rate':
            finalLabel = (
               <p>
                  <strong>Formula:</strong> ROAS = (Total Units Returned / Total
                  Orders) X 100
               </p>
            );
            break;

         case 'order_revenue':
            finalLabel = (
               <div>
                  <p>
                     The total revenue from orders (excluding canceled ones) for
                     a specific date.
                  </p>
                  <p>
                     <strong>Formula:</strong> Gross Sales - Discounts + Taxes +
                     Shipping
                  </p>
               </div>
            );
            break;
         case 'fixed_expenses':
            finalLabel = (
               <div>
                  <p>
                     Fixed expenses refer to the portion of recurring costs
                     allocated to a specific period, calculated by distributing
                     the total cost over its defined recurring period,
                     applicable only to ad spend.
                  </p>
                  <p>
                     <strong>Formula:</strong> Sum(Metric Value X Percent/100)
                  </p>
               </div>
            );
            break;
         case 'variable_expenses':
            finalLabel = (
               <div>
                  <p>
                     Variable expenses refer to the costs that are proportional
                     to specific business metrics, such as sales, orders, and ad
                     spend, calculated based on predefined percentages for a
                     specific period.
                  </p>
                  <p>
                     <strong>Formula:</strong> Cost/ Recurring Days
                  </p>
               </div>
            );
            break;
         case 'payment_gateway_cost':
            finalLabel = (
               <div>
                  <p>
                     Payment gateway fees refer to the charges incurred from
                     different payment methods, including flat fees or
                     percentage-based fees, calculated for orders processed
                     through specific gateways for a given period.
                  </p>
                  <p>
                     <strong>Formula:</strong> Sum(Fee for Payment Method)
                  </p>
               </div>
            );
            break;
         case 'taxes':
            finalLabel = (
               <div>
                  <p>
                     The total taxes refer to the overall tax amount applied to
                     orders within a given period.
                  </p>
               </div>
            );
            break;
         case 'shipping_charges':
            finalLabel = (
               <div>
                  <p>
                     The total shipping charges refer to the overall cost
                     incurred for shipping orders within a given period.
                  </p>
               </div>
            );
            break;
         case 'discounts':
         case 'amazon_discounts':
            finalLabel = (
               <div>
                  <p>
                     The total discounts refer to the overall reduction in order
                     value applied within a given period.
                  </p>
               </div>
            );
            break;
         case 'amazon_return_amount':
            finalLabel = (
               <div>
                  <p>
                     The Amazon Return Amount KPI quantifies the total amount of
                     product returns within a specified period.
                  </p>
               </div>
            );
            break;
         case 'amazon_shipping_charges':
            finalLabel = (
               <div>
                  <p>
                     The Amazon Shipping Charges KPI calculates the total
                     shipping fees collected for orders placed within a
                     specified period.
                  </p>
               </div>
            );
            break;
         case 'amazon_taxes':
            finalLabel = (
               <div>
                  <p>
                     The Amazon Tax Amount KPI calculates the total taxes
                     collected from customers for orders placed within a
                     specified period.
                  </p>
               </div>
            );
            break;
         case 'amazon_total_returns':
            finalLabel = (
               <div>
                  <p>
                     The Total Returned Units KPI calculates the total number of
                     units returned by customers within a specified period
                  </p>
               </div>
            );
            break;
         case 'amazon_total_sales':
            finalLabel = (
               <div>
                  <p>
                     Value for your Amazon orders on a specific date, excluding
                     any canceled orders.
                  </p>
                  <p>
                     <strong>Formula:</strong> Gross Sales - Shipping Fees -
                     Taxes
                  </p>
               </div>
            );
            break;
         case 'gross_sales':
            finalLabel = (
               <div>
                  <p>
                     The total sales revenue generated from all customer
                     purchases before any deductions such as returns, discounts,
                     or allowances.
                  </p>
               </div>
            );
            break;
         case 'net_revenue':
         case 'blended_net_revenue':
            finalLabel = (
               <div>
                  <p>
                     The total net sales from all channels after all deductions,
                     including returns, discounts, and other adjustments.
                  </p>
               </div>
            );
            break;
         case 'blended_returns':
            finalLabel = (
               <div>
                  <p>The total value of returned products from all channels.</p>
               </div>
            );
            break;
         case 'cogs':
            finalLabel = (
               <div>
                  <h3> COGS (Cost of Goods Sold): </h3>
                  <p>
                     The direct cost incurred to produce goods or services sold,
                     calculated as a percentage of total sales revenue.
                  </p>
                  <p>
                     <strong>COGS = </strong> (COGS% X Blended Gross Sales) -
                     (COGS% X Blended Returns)
                  </p>
               </div>
            );
            break;
         case 'net_profit':
            finalLabel = (
               <div>
                  <p>
                     The profit remaining after deducting all expenses including
                     returns, COGS, taxes, shipping, and payment gateway fees
                     from total order revenue.
                  </p>
                  <p>
                     <strong>Net Profit = </strong> Order Revenue - Returns -
                     (COGS + Taxes + Expenses + Shipping Charges + Payment
                     Gateway)
                  </p>
               </div>
            );
            break;
         case 'ebitda':
            finalLabel = (
               <div>
                  <p>
                     Earnings before interest, taxes, depreciation, and
                     amortization. A measure of operational profitability.
                  </p>
                  <p>
                     <strong>EBITDA = </strong> Net Revenue - (COGS + Marketing
                     Costs)
                  </p>
               </div>
            );
            break;
         case 'net_margin':
            finalLabel = (
               <div>
                  <p>
                     The percentage of net profit relative to order revenue,
                     indicating overall profitability.
                  </p>
                  <p>
                     <strong>Net Margin = </strong> (Net Profit / Order Revenue)
                     * 100
                  </p>
               </div>
            );
            break;
         case 'customer_compensations':
            finalLabel = (
               <div>
                  <p>
                     The total of refunds and discounts provided to customers.
                  </p>
                  <p>
                     <strong>Customer Compensations = </strong> Refunds Value +
                     Discounts Value
                  </p>
               </div>
            );
            break;
         case 'google_cost_per_acquisition':
            finalLabel = (
               <div>
                  <p>
                     Cost per Acquisition (CPA) refers to the amount of money
                     spent on ads to achieve a single conversion, calculated by
                     dividing the total cost by the total number of conversions
                     for a specific period.
                  </p>
                  <p>
                     <strong>Formula:</strong> CPA = Total Cost/Total
                     Conversions
                  </p>
               </div>
            );
            break;
         case 'amazon_ads_total_purchase':
            finalLabel = (
               <div>
                  <p>
                     Total purchases refer to the sum of all purchases made
                     during a specific period, calculated from the
                     campaign-level data for Amazon ads.
                  </p>
                  <p>
                     <strong>Formula:</strong> Sum(Purchases Value for the Day)
                  </p>
               </div>
            );
            break;
         case 'total_purchase':
            finalLabel = (
               <div>
                  <p>
                     Total number of purchase events tracked from Meta ad
                     campaigns
                  </p>
                  <p>
                     <strong>Formula:</strong> Total Purchases= Sum of Purchases
                     from Meta Ads
                  </p>
               </div>
            );
            break;
         case 'amazon_gross_sales':
            finalLabel = (
               <div>
                  <p>
                     Total revenue generated from sales on Amazon before
                     deducting returns, discounts, or fees.
                  </p>
                  <p>
                     <strong>Formula:</strong> Total Units Sold X Price per Unit
                  </p>
               </div>
            );
            break;
         case 'mer':
            finalLabel = (
               <div>
                  <p>
                     Evaluates overall marketing efficiency by comparing total
                     revenue to total ad spend.
                  </p>
                  <p>
                     <strong>Formula:</strong> (Total Ad Spend / Total Revenue)
                     * 100
                  </p>
               </div>
            );
            break;
         case 'blended_cpa':
            finalLabel = (
               <div>
                  <p>
                     Measures the average cost to acquire a customer across all
                     advertising platforms
                  </p>
                  <p>
                     <strong>Formula:</strong>Total Ad Spend / Total Conversions
                     (Orders or Leads)
                  </p>
               </div>
            );
            break;
         case 'blended_roas':
            finalLabel = (
               <div>
                  <p>
                     Measures revenue generated per dollar spent on advertising
                     across multiple platforms.
                  </p>
                  <p>
                     <strong>Formula:</strong>Total Revenue / Total Ad Spend
                  </p>
               </div>
            );
            break;
         case 'total_sessions':
            finalLabel = (
               <p>The total number of sessions, or visits, to your website.</p>
            );
            break;
         case 'blended_ad_spend':
            finalLabel = (
               <p>
                  Total advertising spend across all platforms for promoting
                  products.
               </p>
            );
            break;
         case 'blended_total_orders':
            finalLabel = (
               <p>Total orders across all platforms for promoting products.</p>
            );
            break;
         case 'blended_total_revenue':
            finalLabel = (
               <p>Total revenue across all platforms for promoting products.</p>
            );
            break;
         case 'amazon_total_sessions':
            finalLabel = (
               <p>The total number of sessions or visits to your website</p>
            );
            break;
         case 'total_spent':
            finalLabel = (
               <p>
                  The total amount of money spent on advertising or campaigns.
               </p>
            );
            break;
         case 'google_total_spend':
            finalLabel = (
               <p>
                  The total amount of money spent on advertising or campaigns on
                  a specific date.
               </p>
            );
            break;
         case 'amazon_ads_spent':
            finalLabel = (
               <p>
                  The total amount of money spent on advertising or campaigns
               </p>
            );
            break;
         case 'total_users':
            finalLabel = (
               <p>The total number of users who have visited your website.</p>
            );
            break;
         default:
            finalLabel = <p>Unknown KPI: Please provide a valid KPI name.</p>;
            break;
      }
   } else if (category) {
      switch (category) {
         case 'web':
            finalLabel = (
               <div style={{ margin: 5 }}>
                  <h2 style={{ fontSize: 20 }}>Web Analytics</h2>
                  <p>
                     <strong>Source of this data:</strong>
                     <br />
                     This data is not derived from Flable AI. The information on
                     the Summary page is real-time data directly pulled from
                     your web analytics tools.
                     <br />
                     We display this real-time data here, which is sourced
                     directly from your analytics accounts, and use it to
                     generate live metrics in the Custom Metrics section above.
                  </p>
                  <p>
                     <strong>
                        Do these metrics match what you see in your analytics
                        tools?
                     </strong>
                     <br />
                     Not necessarily! For details on how each metric is
                     calculated, hover over the ❓ icon located at the top of
                     each tile.
                  </p>
               </div>
            );

            break;
         case 'facebookads':
            finalLabel = (
               <div style={{ margin: 5 }}>
                  <h2 style={{ fontSize: 20 }}>Facebook Ads</h2>
                  <p>
                     <strong>Source of this data:</strong>
                     <br />
                     The data presented here is not from Flable AI. Instead, all
                     the information on the Summary page comes directly from the
                     ad channel accounts, providing native data from Facebook.
                     <br />
                     We receive these numbers from Facebook and display them
                     here, using them to generate aggregated metrics in the
                     Custom Metrics section above.
                  </p>
                  <p>
                     {/* <strong>Regarding Pixel data:</strong>
                     <br />
                     For metrics that we've tracked using our Pixel for
                     Facebook, including first-party attribution, please visit
                     the Pixel &gt; Ads page. */}
                     <strong>
                        Do these metrics match those in Ads account?
                     </strong>
                     <br />
                     Not necessarily! To understand how each metric is
                     calculated, hover your mouse over the ❓ icon at the top of
                     each tile.
                  </p>
               </div>
            );
            break;
         case 'googleads':
            finalLabel = (
               <div style={{ margin: 5 }}>
                  <h2 style={{ fontSize: 20 }}>Google Ads</h2>
                  <p>
                     <strong>Source of this data:</strong>
                     <br />
                     The data presented here is not from Flable AI. Instead, all
                     the information on the Summary page comes directly from the
                     ad channel accounts, providing native data from Google.
                     <br />
                     We receive these numbers from Google and display them here,
                     using them to generate aggregated metrics in the Custom
                     Metrics section above.
                  </p>
                  <p>
                     {/* <strong>Regarding Pixel data:</strong>
                     <br />
                     For metrics that we've tracked using our Pixel for
                     Facebook, including first-party attribution, please visit
                     the Pixel &gt; Ads page. */}
                     <strong>
                        Do these metrics match those in Ads account?
                     </strong>
                     <br />
                     Not necessarily! To understand how each metric is
                     calculated, hover your mouse over the ❓ icon at the top of
                     each tile.
                  </p>
               </div>
            );
            break;
         case 'amazon_selling_partner':
            finalLabel = (
               <div style={{ margin: 5 }}>
                  <h2 style={{ fontSize: 20 }}>Amazon Selling Partner</h2>
                  <p>
                     <strong>Source of this data:</strong>
                     <br />
                     The data presented here is not from Flable AI. Instead, all
                     the information on the Summary page comes directly from the
                     ad channel accounts, providing native data from Amazon.
                     <br />
                     We receive these numbers from Amazon and display them here,
                     using them to generate aggregated metrics in the Custom
                     Metrics section above.
                  </p>
                  <p>
                     {/* <strong>Regarding Pixel data:</strong>
                     <br />
                     For metrics that we've tracked using our Pixel for
                     Facebook, including first-party attribution, please visit
                     the Pixel &gt; Ads page. */}
                     <strong>
                        Do these metrics match those in Ads account?
                     </strong>
                     <br />
                     Not necessarily! To understand how each metric is
                     calculated, hover your mouse over the ❓ icon at the top of
                     each tile.
                  </p>
               </div>
            );
            break;
         case 'amazon_ads':
            finalLabel = (
               <div style={{ margin: 5 }}>
                  <h2 style={{ fontSize: 20 }}>Amazon Ads</h2>
                  <p>
                     <strong>Source of this data:</strong>
                     <br />
                     The data presented here is not from Flable AI. Instead, all
                     the information on the Summary page comes directly from the
                     ad channel accounts, providing native data from Amazon.
                     <br />
                     We receive these numbers from Amazon and display them here,
                     using them to generate aggregated metrics in the Custom
                     Metrics section above.
                  </p>
                  <p>
                     {/* <strong>Regarding Pixel data:</strong>
                     <br />
                     For metrics that we've tracked using our Pixel for
                     Facebook, including first-party attribution, please visit
                     the Pixel &gt; Ads page. */}
                     <strong>
                        Do these metrics match those in Ads account?
                     </strong>
                     <br />
                     Not necessarily! To understand how each metric is
                     calculated, hover your mouse over the ❓ icon at the top of
                     each tile.
                  </p>
               </div>
            );
            break;
         case 'store':
            finalLabel = (
               <div style={{ margin: 5 }}>
                  <h2 style={{ fontSize: 20 }}>Shopify Store</h2>
                  <p>
                     <strong>Source of this data:</strong>
                     <br />
                     This data does not originate from Flable AI. The
                     information on the Summary page is channel-native, meaning
                     it is imported directly from your ad channel accounts.
                     <br />
                     In this case, Shopify provides data from your store, which
                     we display here and use to generate aggregated metrics in
                     the Custom Metrics section above.
                  </p>
                  <p>
                     <strong>Do these metrics match those in Shopify?</strong>
                     <br />
                     Not necessarily! To understand how each metric is
                     calculated, hover your mouse over the ❓ icon at the top of
                     each tile.
                  </p>
               </div>
            );
            break;
         case 'overall_metrics':
            finalLabel = (
               <div style={{ margin: 5 }}>
                  <h2 style={{ fontSize: 20 }}>Overall Metrics</h2>
                  <p>
                     <strong>Source of this data:</strong>
                     <br />
                     This data does not originate from Flable AI. The
                     information on the Summary page is channel-native, meaning
                     it is imported directly from all of your ad channel
                     accounts from all platforms.
                     <br />
                  </p>
                  <p>
                     <strong>Do these metrics match?</strong>
                     <br />
                     Not necessarily! To understand how each metric is
                     calculated, hover your mouse over the ❓ icon at the top of
                     each tile.
                  </p>
               </div>
            );

            break;
         case 'pinned':
            finalLabel = (
               <div style={{ margin: 5 }}>
                  <h2 style={{ fontSize: 20 }}>Pinned</h2>
                  <p>
                     <strong>See all your Pinned KPIs here</strong>
                     <br />
                     You can add/remove KPIs by clicking plus icon.
                     <br />
                     You can also add/remove KPI by clicking on pin icon at the
                     right top of each tile.
                  </p>
               </div>
            );
            break;
         default:
            finalLabel = (
               <p>Unknown Category: Please provide a valid Category name.</p>
            );
            break;
      }
   }

   return (
      <Tooltip label={finalLabel} hasArrow={hasArrow} placement={placement}>
         <span>{children}</span>
      </Tooltip>
   );
}

export default TooltipContent;
