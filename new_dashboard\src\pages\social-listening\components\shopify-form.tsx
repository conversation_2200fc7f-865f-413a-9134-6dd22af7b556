/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/no-unsafe-member-access */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */
/* eslint-disable @typescript-eslint/no-misused-promises */
import React, { useState, useCallback, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { connectDisconnectToShopify } from '../utils';
import { channelNames, shopifyIntegrationSteps } from '../utils/constant';
import { Keys, LocalStorageService } from '../../../utils/local-storage';
import { NewCommerceIntegrationLayout } from './seller-panel-components/new-commerce-integration-layout';
import { ShopifySellerPanel } from './seller-panel-components/shopify-panel';
import image from '../images/integrations/shopify.png';
import { AuthUser } from '../../../types/auth';

interface FormFields {
   channelName: string;
   storeName: string;
   apiKey: string;
   apiSecret: string;
   adminAccessToken: string;
}

interface ApiError {
   success: boolean;
   message: string;
}

const ShopifyForm: React.FC = () => {
   const navigate = useNavigate();
   const [trying, setTrying] = useState<boolean>(false);
   const [apiError, setApiError] = useState<ApiError | null>(null);

   const client_id = LocalStorageService.getItem<AuthUser>(
      Keys.FlableUserDetails,
   )?.client_id;

   const [unmounted, setUnmounted] = useState<boolean>(false);

   const defaultState: FormFields = {
      channelName: channelNames.SHOPIFY,
      storeName: '',
      apiKey: '',
      apiSecret: '',
      adminAccessToken: '',
   };

   const [formFields, setFormFields] = useState<FormFields>(defaultState);

   const handleChange = useCallback(
      (e: React.ChangeEvent<HTMLInputElement>) => {
         if (unmounted) return;
         const { name, value } = e.target;
         setFormFields((prev) => ({ ...prev, [name]: value }));
      },
      [unmounted],
   );

   const handleConnect = useCallback(
      async (e: React.FormEvent<HTMLFormElement>) => {
         if (!client_id) return;
         e.preventDefault();
         if (unmounted) return;
         const { apiSecret, apiKey, storeName, adminAccessToken } = formFields;
         try {
            setTrying(true);
            setApiError({
               success: false,
               message: '',
            });

            await connectDisconnectToShopify({
               channel_name: channelNames.SHOPIFY,
               client_id,
               channel_client_key: apiKey,
               channel_secret: apiSecret,
               admin_access_token: adminAccessToken,
               store_url: `${storeName}.myshopify.com`,
               isConnect: true,
            });
            setFormFields(defaultState);
            setApiError({
               success: true,
               message: 'Connection Established, Redirecting...',
            });
            setTimeout(() => {
               navigate('/integrations');
            }, 3000);
         } catch (err) {
            const error = err as any;

            const errMessage =
               error.response?.data?.message || 'Error connecting to Shopify';
            setApiError({
               success: false,
               message: errMessage,
            });
         } finally {
            setTrying(false);
         }
      },
      [formFields, history, defaultState, unmounted],
   );

   useEffect(() => {
      setUnmounted(false);
      return () => {
         setUnmounted(true);
         setFormFields(defaultState);
         setApiError(null);
      };
   }, []);

   return (
      <NewCommerceIntegrationLayout
         title='Shopify'
         description='Connect your Shopify account to manage your logistics seamlessly'
         logo={image}
         logoAlt='Shopify Logo'
         steps={shopifyIntegrationSteps}
      >
         <ShopifySellerPanel
            title='Seller Panel'
            description='Please provide the following credentials for Shopify:'
            storeName={formFields.storeName}
            apiKey={formFields.apiKey}
            apiSecret={formFields.apiSecret}
            adminAccessToken={formFields.adminAccessToken}
            onChange={handleChange}
            onSubmit={handleConnect}
            isLoading={trying}
            apiResponse={apiError}
            submitButtonText='Connect to Shopify'
            loadingText='Connecting to  Shopify...'
         />
      </NewCommerceIntegrationLayout>
   );
};

export default ShopifyForm;
