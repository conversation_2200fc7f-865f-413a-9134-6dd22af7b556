import { useNavigate, useLocation } from 'react-router-dom';
import { useEffect, useState } from 'react';
import Card from './Card';
import image from '../images/integrations/shiprocket.png';
import endPoints from '../apis/agent';
import { connectDisconnectToShipRocket } from '../utils';
import { Keys, LocalStorageService } from '../../../utils/local-storage';
import { AuthUser } from '../../../types/auth';

interface ConnectionDetails {
   is_active?: boolean;
   store?: string;
}

const ShipRocket = () => {
   const navigate = useNavigate();
   const location = useLocation();

   const client_id = LocalStorageService.getItem<AuthUser>(
      Keys.FlableUserDetails,
   )?.client_id;
   const [connectionDetails, setConnectionDetails] =
      useState<ConnectionDetails>({});
   const [isFetching, setIsFetching] = useState<boolean>(false);

   function handleNavigation() {
      if (location.pathname === '/onboarding') {
         navigate('/onboarding/shiprocket');
      } else {
         navigate('/integrations/shiprocket');
      }
   }

   useEffect(() => {
      if (!client_id) return;
      const fetchData = async () => {
         try {
            setIsFetching(true);
            const {
               data: { details },
            } = await endPoints.checkConnectionDetails({
               client_id,
               channel_name: 'shiprocket',
            });
            const { is_active, store } = details || {};
            setConnectionDetails({ is_active, store });
         } catch (error) {
            console.error('Error fetching connection details:', error);
         } finally {
            setIsFetching(false);
         }
      };

      void fetchData();
   }, []);

   async function handleDisconnect() {
      if (!client_id) return;

      try {
         const isConfirmed = confirm('Are you sure?');

         if (!isConfirmed) return;

         await connectDisconnectToShipRocket({
            channel_name: 'shiprocket',
            client_id,
            isConnect: false,
         });
         setConnectionDetails({});
      } catch (err) {
         console.log(err);
      }
   }

   return (
      <Card
         isConnected={connectionDetails.is_active}
         isFetching={isFetching}
         heading={
            connectionDetails.store ? connectionDetails.store : 'Shiprocket'
         }
         src={image}
         onButtonClick={
            connectionDetails.is_active ? handleDisconnect : handleNavigation
         }
      />
   );
};

export default ShipRocket;
