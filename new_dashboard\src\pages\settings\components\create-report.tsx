import {
   Box,
   Button,
   Checkbox,
   Collapse,
   Flex,
   Heading,
   Input,
   Menu,
   MenuButton,
   MenuItem,
   MenuList,
   Popover,
   PopoverBody,
   PopoverContent,
   PopoverTrigger,
   Skeleton,
   Spinner,
   Stack,
   Text,
   useToast,
} from '@chakra-ui/react';
import { useForm, SubmitHandler } from 'react-hook-form';
import {
   FREQUENCY,
   INTERVALS,
   SUB_INTERVALS,
} from '../../dashboard/utils/default-variables';
import {
   ChangeEvent,
   Dispatch,
   SetStateAction,
   useEffect,
   useState,
} from 'react';
import { useApiMutation, useApiQuery } from '../../../hooks/react-query-hooks';
import KPIQueryKeys, {
   SettingsQueryKeys,
} from '../../dashboard/utils/query-keys';
import { Keys, LocalStorageService } from '../../../utils/local-storage';
import kpiService from '../../../api/service/kpi/index';
import settingsService from '../../../api/service/settings/index';
import { KPIMeta, MetricReport } from '../../dashboard/utils/interface';
import { getAggData, getUserDate } from '../../dashboard/utils/helpers';
import { KPICategory } from '../../../utils/strings/kpi-constants';
import { FaChevronDown, FaChevronUp } from 'react-icons/fa';
import { VALID_EMAILS_PATTERN } from '../../../utils/strings/settings-strings';
import FrequencyImage from './frequency-image';
import { ChevronDownIcon, ChevronRightIcon } from '@chakra-ui/icons';
import { Calendar } from 'react-date-range';
type FormValues = {
   title: string;
   frequency: string;
   time: string;
   emails: string;
   interval: string;
   start_date: string;
   end_date: string;
};

interface CreateReportProps {
   report: MetricReport;
   seteditReport: Dispatch<SetStateAction<MetricReport>>;
   setallReports: Dispatch<SetStateAction<boolean>>;
}
function CreateReport(props: CreateReportProps) {
   const {
      register,
      setValue,
      formState: { errors },
      handleSubmit,
      watch,
   } = useForm<FormValues>({
      defaultValues: {
         time: props.report?.time,
         title: props.report?.title,
         frequency: props.report?.frequency,
         emails: props.report?.emails,
         interval: props.report?.interval,
         start_date: props.report?.start_date,
         end_date: props.report?.end_date,
      },
   });
   const [filteredKpis, setfilteredKpis] = useState<KPIMeta[]>([]);
   const [collapseCat, setcollapseCat] = useState<{
      [key: string]: boolean;
   }>({});
   const [selKpisError, setselKpisError] = useState<string>('');
   //const [openInterval, setopenInterval] = useState<boolean>(false);
   const [selMetrics, setselMetrics] = useState<KPIMeta[]>(
      props.report?.kpis
         ? (JSON.parse(props.report?.kpis as string) as KPIMeta[])
         : [],
   );
   const reportTitle = watch('title');
   const reportFreqency = watch('frequency');
   const reportInterval = watch('interval');
   const reportStart = watch('start_date');
   const reportEnd = watch('end_date');
   const userDetails: {
      [key: string]: string;
   } = LocalStorageService.getItem(Keys.FlableUserDetails) || {};
   const isAutoReport = props.report?.is_auto_report === true;
   const onSubmit: SubmitHandler<FormValues> = (data): void => {
      if (!selMetrics.length) {
         setselKpisError('Please select atleast one metric');
         return;
      }

      const date = new Date();
      date.setDate(date.getDate() - 1);

      sendEmailReport({
         clientId: LocalStorageService.getItem(Keys.ClientId) as string,
         user_id: userDetails.user_id,
         title: data.title,
         time: data.time,
         frequency: data.frequency,
         interval: data.interval,
         start_date: data.start_date,
         end_date: data.end_date,
         date: date.toISOString(),
         emails: data.emails,
         kpis: selMetrics,
         username: userDetails.fullName,
         reportId: '',
      });
   };
   const onSave = (data: FormValues): void => {
      if (!selMetrics.length) {
         setselKpisError('Please select atleast one metric');
         return;
      }

      const date = new Date();

      if (props.report.report_id) {
         updateEmailReport({
            clientId: LocalStorageService.getItem(Keys.ClientId) as string,
            user_id: userDetails.user_id,
            title: data.title,
            time: data.time,
            frequency: data.frequency,
            interval: data.interval,
            start_date: data.start_date,
            end_date: data.end_date,
            date: date.toISOString(),
            emails: data.emails,
            kpis: selMetrics.map((x) => {
               return {
                  kpi: x.kpi,
                  category: x.category,
                  kpi_display_name: x.kpi_display_name,
               };
            }),
            username: userDetails.fullName,
            reportId: props.report.report_id || '',
         });
      } else {
         createEmailReport({
            clientId: LocalStorageService.getItem(Keys.ClientId) as string,
            user_id: userDetails.user_id,
            title: data.title,
            time: data.time,
            frequency: data.frequency,
            interval: data.interval,
            start_date: data.start_date,
            end_date: data.end_date,
            date: date.toISOString(),
            emails: data.emails,
            kpis: selMetrics.map((x) => {
               return {
                  kpi: x.kpi,
                  category: x.category,
                  kpi_display_name: x.kpi_display_name,
               };
            }),
            username: userDetails.fullName,
            reportId: '',
            is_auto_report: false,
            is_subscribed: true,
         });
      }
      setTimeout(() => handleCancel(), 1000);
   };
   const toast = useToast();
   useEffect(() => {
      getMetaData()
         .then((res) => {
            setfilteredKpis(res.data || []);
         })
         .catch(console.log);
   }, []);
   const {
      data: metaData,
      isFetching: metaLoad,
      refetch: getMetaData,
   } = useApiQuery({
      queryKey: [KPIQueryKeys.kpiMeta],
      queryFn: () =>
         kpiService.getKpiMeta(
            LocalStorageService.getItem(Keys.ClientId) as string,
         ),
      getInitialData: () => [],
      enabled: false,
   });

   const handleReportSuccess = () => {
      toast({
         title: 'Success',
         description: 'Report sent successfully',
         status: 'success',
         duration: 5000,
         isClosable: true,
      });
   };
   const handleCreateReportSuccess = () => {
      toast({
         title: 'Success',
         description: 'Report created successfully',
         status: 'success',
         duration: 5000,
         isClosable: true,
      });
   };
   const handleUpdateReportSuccess = () => {
      toast({
         title: 'Success',
         description: 'Report updated successfully',
         status: 'success',
         duration: 5000,
         isClosable: true,
      });
   };

   const { mutate: sendEmailReport, isPending: mailLoading } = useApiMutation({
      queryKey: [SettingsQueryKeys.sendEmailReport],
      mutationFn: settingsService.sendReportMail,
      onSuccessHandler: handleReportSuccess,
      onError: handleError,
   });
   const { mutate: createEmailReport, isPending: createMailLoading } =
      useApiMutation({
         queryKey: [SettingsQueryKeys.createEmailReport],
         mutationFn: settingsService.createReport,
         onSuccessHandler: handleCreateReportSuccess,
         onError: handleError,
      });
   const { mutate: updateEmailReport, isPending: updateMailLoading } =
      useApiMutation({
         queryKey: [SettingsQueryKeys.updateEmailReport],
         mutationFn: settingsService.updateReport,
         onSuccessHandler: handleUpdateReportSuccess,
         onError: handleError,
      });

   function handleError(e: string | null) {
      toast({
         title: 'Error',
         description: e || 'Some error occured',
         status: 'error',
         duration: 5000,
         isClosable: true,
      });
   }
   const handleCategoryCheck = (
      e: ChangeEvent<HTMLInputElement>,
      category: string,
   ) => {
      if (!e.target.checked) {
         setselMetrics((prev) => prev.filter((x) => x.category != category));
      } else {
         setselMetrics((prev) =>
            prev
               .filter((x) => x.category != category)
               .concat(metaData?.filter((x) => x.category == category) || []),
         );
         setselKpisError('');
      }
   };
   const handleMetricCheck = (
      e: ChangeEvent<HTMLInputElement>,
      kpi: KPIMeta,
   ) => {
      if (!e.target.checked) {
         setselMetrics((prev) =>
            prev.filter(
               (x) => `${x.category}-${x.kpi}` !== `${kpi.category}-${kpi.kpi}`,
            ),
         );
      } else {
         setselMetrics((prev) => prev.concat([kpi]));
         setselKpisError('');
      }
   };
   const handleCancel = () => {
      props.setallReports(true);
      props.seteditReport({} as MetricReport);
   };
   return (
      <Box p={4} pt={0}>
         <Heading fontSize={'30px'} fontWeight={'500'}>
            {reportTitle || 'New Report'}
         </Heading>
         <Heading fontSize={'18px'} fontWeight={'500'} pt={4}>
            General
         </Heading>
         <form onSubmit={handleSubmit(onSubmit) as () => void}>
            <Flex py={4} direction={'column'} gap={8}>
               <Flex direction={'column'} gap={3}>
                  <Flex width={'100%'} gap={3} justifyContent={'space-between'}>
                     <Flex direction={'column'} flex={1}>
                        <Text fontSize={'14px'} fontWeight={'700'}>
                           Report title
                        </Text>
                        <Flex direction={'column'} flex={1}>
                           <Input
                              aria-invalid={errors.title ? true : false}
                              {...register('title', {
                                 required: !isAutoReport
                                    ? 'Title is required'
                                    : false,
                              })}
                              className='inputs'
                              disabled={isAutoReport}
                              placeholder='Enter report title'
                           />
                           {errors.title && (
                              <p className='err-message' role='alert'>
                                 {'  '}
                                 {errors.title?.message}
                              </p>
                           )}
                        </Flex>
                     </Flex>
                     <Flex direction={'column'} flex={1}>
                        <Text fontSize={'14px'} fontWeight={'700'}>
                           Select Time
                        </Text>
                        <Flex direction={'column'} flex={1}>
                           <Input
                              className='inputs'
                              aria-invalid={errors.time ? true : false}
                              placeholder='Time'
                              disabled={isAutoReport}
                              type='time'
                              {...register('time', {
                                 required: 'Time is required',
                              })}
                           />
                           {errors.time && (
                              <p className='err-message' role='alert'>
                                 {'  '}
                                 {errors.time?.message}
                              </p>
                           )}
                        </Flex>
                     </Flex>
                  </Flex>
                  <Flex width={'100%'} gap={3} justifyContent={'space-between'}>
                     <Flex direction={'column'} flex={1}>
                        <Text fontSize={'14px'} fontWeight={'700'}>
                           Select Frequency
                        </Text>
                        <Flex direction={'column'}>
                           <Menu matchWidth>
                              <MenuButton
                                 as={Button}
                                 isDisabled={isAutoReport}
                                 rightIcon={
                                    <ChevronDownIcon fontSize={'20px'} />
                                 }
                                 width='100%'
                                 textAlign='left'
                                 height={'45px'}
                                 fontSize={'14px'}
                                 bg='white'
                                 border='1px solid #CBD5E0'
                                 _hover={{ bg: 'none' }}
                                 _focus={{
                                    outline: 'none',
                                    borderColor: '#3182ce',
                                    borderWidth: '2px',
                                 }}
                              >
                                 {reportFreqency ? (
                                    <Text
                                       display={'flex'}
                                       gap={2}
                                       alignItems={'center'}
                                    >
                                       {' '}
                                       <FrequencyImage
                                          frequency={reportFreqency}
                                       />
                                       {FREQUENCY[reportFreqency] ||
                                          reportFreqency}
                                    </Text>
                                 ) : (
                                    'None'
                                 )}
                              </MenuButton>
                              <MenuList>
                                 {Object.entries(FREQUENCY).map(
                                    ([key, label]) => (
                                       <MenuItem
                                          key={key}
                                          onClick={() => {
                                             setValue('frequency', key, {
                                                shouldValidate: true,
                                             });
                                             setValue('interval', '');
                                          }}
                                          gap={2}
                                          _hover={{ bg: 'gray.200' }}
                                          isDisabled={key === 'year'}
                                       >
                                          <FrequencyImage frequency={key} />
                                          {label}
                                       </MenuItem>
                                    ),
                                 )}
                              </MenuList>
                           </Menu>
                           <input
                              type='hidden'
                              {...register('frequency', {
                                 required: 'Frequency is required',
                              })}
                           />

                           {errors.frequency && (
                              <p className='err-message' role='alert'>
                                 {'  '}
                                 {errors.frequency?.message}
                              </p>
                           )}
                        </Flex>
                     </Flex>
                     <Flex direction={'column'} flex={1}>
                        <Text fontSize={'14px'} fontWeight={'700'}>
                           Choose Interval
                        </Text>
                        <Flex direction={'column'}>
                           <Menu matchWidth closeOnSelect={false}>
                              {({ onClose }) => (
                                 <>
                                    <MenuButton
                                       isDisabled={
                                          !reportFreqency || isAutoReport
                                       }
                                       as={Button}
                                       rightIcon={
                                          <ChevronDownIcon fontSize={'20px'} />
                                       }
                                       width='100%'
                                       textAlign='left'
                                       height={'45px'}
                                       fontSize={'14px'}
                                       bg='white'
                                       border='1px solid #CBD5E0'
                                       _hover={{ bg: 'none' }}
                                       _focus={{
                                          outline: 'none',
                                          borderColor: '#3182ce',
                                          borderWidth: '2px',
                                       }}
                                    >
                                       {reportInterval ? (
                                          <Text
                                             display={'flex'}
                                             gap={2}
                                             alignItems={'center'}
                                          >
                                             {' '}
                                             <FrequencyImage
                                                frequency={'hourglass'}
                                             />
                                             {reportInterval}
                                          </Text>
                                       ) : (
                                          'None'
                                       )}
                                    </MenuButton>
                                    <MenuList>
                                       {INTERVALS[reportFreqency]?.map(
                                          (label) =>
                                             SUB_INTERVALS[reportFreqency] ? (
                                                <Menu
                                                   key={label}
                                                   placement='right-start'
                                                >
                                                   <MenuButton
                                                      type='button'
                                                      width='100%'
                                                      justifyContent='space-between'
                                                      py={1}
                                                      px={2}
                                                      textAlign='left'
                                                      _hover={{
                                                         bg: 'gray.200',
                                                      }}
                                                   >
                                                      {label}{' '}
                                                      <ChevronRightIcon
                                                         fontSize={'24px'}
                                                      />
                                                   </MenuButton>
                                                   <MenuList>
                                                      {SUB_INTERVALS[
                                                         reportFreqency
                                                      ]?.map((item) => (
                                                         <MenuItem
                                                            key={item}
                                                            onClick={() => {
                                                               setValue(
                                                                  'interval',
                                                                  `${label}, ${item}`,
                                                                  {
                                                                     shouldValidate: true,
                                                                  },
                                                               );
                                                               onClose();
                                                            }}
                                                         >
                                                            {item}
                                                         </MenuItem>
                                                      ))}
                                                   </MenuList>
                                                </Menu>
                                             ) : (
                                                <MenuItem
                                                   key={label}
                                                   onClick={() =>
                                                      setValue(
                                                         'interval',
                                                         label,
                                                         {
                                                            shouldValidate: true,
                                                         },
                                                      )
                                                   }
                                                   gap={2}
                                                   _hover={{ bg: 'gray.200' }}
                                                >
                                                   {label}
                                                </MenuItem>
                                             ),
                                       )}
                                    </MenuList>
                                 </>
                              )}
                           </Menu>
                           {errors.interval && (
                              <p className='err-message' role='alert'>
                                 {'  '}
                                 {errors.interval?.message}
                              </p>
                           )}
                           <input
                              type='hidden'
                              {...register('interval', {
                                 required: 'Interval is required',
                              })}
                           />
                        </Flex>
                     </Flex>
                     <Flex direction={'column'} flex={1}>
                        <Text fontSize={'14px'} fontWeight={'700'}>
                           Select Start Date
                        </Text>
                        <Flex direction={'column'}>
                           <Popover>
                              {({ onClose }) => (
                                 <>
                                    <PopoverTrigger>
                                       <Button
                                          isDisabled={
                                             !reportFreqency || isAutoReport
                                          }
                                          rightIcon={
                                             <ChevronDownIcon
                                                fontSize={'20px'}
                                             />
                                          }
                                          justifyContent={'space-between'}
                                          width='100%'
                                          textAlign='left'
                                          height={'45px'}
                                          fontSize={'14px'}
                                          bg='white'
                                          border='1px solid #CBD5E0'
                                          _hover={{ bg: 'none' }}
                                          _focus={{
                                             outline: 'none',
                                             borderColor: '#3182ce',
                                             borderWidth: '2px',
                                          }}
                                       >
                                          {reportStart ? (
                                             <Text
                                                display={'flex'}
                                                gap={2}
                                                alignItems={'center'}
                                             >
                                                {' '}
                                                <FrequencyImage
                                                   frequency={'calendar'}
                                                />
                                                {getUserDate(
                                                   new Date(reportStart),
                                                   true,
                                                )}
                                             </Text>
                                          ) : (
                                             'None'
                                          )}
                                       </Button>
                                    </PopoverTrigger>
                                    <PopoverContent>
                                       <PopoverBody p={0}>
                                          <Calendar
                                             date={
                                                reportStart
                                                   ? new Date(reportStart)
                                                   : undefined
                                             }
                                             minDate={new Date()}
                                             onChange={(date) => {
                                                const year = date.getFullYear();
                                                const month = String(
                                                   date.getMonth() + 1,
                                                ).padStart(2, '0'); // Months are 0-based
                                                const day = String(
                                                   date.getDate(),
                                                ).padStart(2, '0');
                                                setValue(
                                                   'start_date',
                                                   `${year}-${month}-${day}`,
                                                   { shouldValidate: true },
                                                );
                                                onClose();
                                             }}
                                          />
                                       </PopoverBody>
                                    </PopoverContent>
                                 </>
                              )}
                           </Popover>
                           {errors.start_date && (
                              <p className='err-message' role='alert'>
                                 {'  '}
                                 {errors.start_date?.message}
                              </p>
                           )}
                           <input
                              type='hidden'
                              {...register('start_date', {
                                 required: 'Start Date is required',
                              })}
                           />
                        </Flex>
                     </Flex>
                     <Flex direction={'column'} flex={1}>
                        <Text fontSize={'14px'} fontWeight={'700'}>
                           Select End Date
                        </Text>
                        <Flex direction={'column'}>
                           <Menu matchWidth closeOnSelect={false}>
                              {({ onClose: MenuClose }) => (
                                 <>
                                    <MenuButton
                                       isDisabled={
                                          !reportFreqency || isAutoReport
                                       }
                                       as={Button}
                                       rightIcon={
                                          <ChevronDownIcon fontSize={'20px'} />
                                       }
                                       width='100%'
                                       textAlign='left'
                                       height={'45px'}
                                       fontSize={'14px'}
                                       bg='white'
                                       border='1px solid #CBD5E0'
                                       _hover={{ bg: 'none' }}
                                       _focus={{
                                          outline: 'none',
                                          borderColor: '#3182ce',
                                          borderWidth: '2px',
                                       }}
                                    >
                                       {reportEnd ? (
                                          <Text
                                             display={'flex'}
                                             gap={2}
                                             alignItems={'center'}
                                          >
                                             {' '}
                                             <FrequencyImage
                                                frequency={'flag'}
                                             />
                                             {reportEnd == 'infinity'
                                                ? 'Forever'
                                                : getUserDate(
                                                     new Date(reportEnd),
                                                     true,
                                                  )}
                                          </Text>
                                       ) : (
                                          'None'
                                       )}
                                    </MenuButton>
                                    <MenuList>
                                       <MenuItem
                                          onClick={() => {
                                             setValue('end_date', 'infinity', {
                                                shouldValidate: true,
                                             });
                                             MenuClose();
                                          }}
                                          gap={2}
                                          _hover={{ bg: 'gray.200' }}
                                       >
                                          <Text
                                             display={'flex'}
                                             gap={2}
                                             alignItems={'center'}
                                          >
                                             {' '}
                                             <FrequencyImage
                                                frequency={'infinity'}
                                             />
                                             {'Forever'}
                                          </Text>
                                       </MenuItem>
                                       <Popover closeOnBlur={false}>
                                          {({ onClose }) => (
                                             <>
                                                <PopoverTrigger>
                                                   <Button
                                                      as={MenuItem}
                                                      px={3}
                                                      justifyContent={
                                                         'space-between'
                                                      }
                                                      width='100%'
                                                      borderRadius={'0'}
                                                      textAlign='left'
                                                      background={'white'}
                                                      _hover={{
                                                         bg: 'gray.200',
                                                      }}
                                                   >
                                                      <Text
                                                         display={'flex'}
                                                         gap={2}
                                                         alignItems={'center'}
                                                         fontWeight={'400'}
                                                      >
                                                         {' '}
                                                         <FrequencyImage
                                                            frequency={
                                                               'calendar'
                                                            }
                                                         />
                                                         {reportEnd &&
                                                         reportEnd !==
                                                            'infinity'
                                                            ? getUserDate(
                                                                 new Date(
                                                                    reportEnd,
                                                                 ),
                                                                 true,
                                                              )
                                                            : 'Pick a specific date'}
                                                      </Text>
                                                   </Button>
                                                </PopoverTrigger>
                                                <PopoverContent>
                                                   <PopoverBody p={0}>
                                                      <Calendar
                                                         date={
                                                            reportEnd !==
                                                               'infinity' &&
                                                            !!reportEnd
                                                               ? new Date(
                                                                    reportEnd,
                                                                 )
                                                               : undefined
                                                         }
                                                         minDate={
                                                            reportStart
                                                               ? new Date(
                                                                    reportStart,
                                                                 )
                                                               : new Date()
                                                         }
                                                         onChange={(date) => {
                                                            const year =
                                                               date.getFullYear();
                                                            const month =
                                                               String(
                                                                  date.getMonth() +
                                                                     1,
                                                               ).padStart(
                                                                  2,
                                                                  '0',
                                                               ); // Months are 0-based
                                                            const day = String(
                                                               date.getDate(),
                                                            ).padStart(2, '0');
                                                            setValue(
                                                               'end_date',
                                                               `${year}-${month}-${day}`,
                                                               {
                                                                  shouldValidate: true,
                                                               },
                                                            );
                                                            onClose();
                                                            MenuClose();
                                                         }}
                                                      />
                                                   </PopoverBody>
                                                </PopoverContent>
                                             </>
                                          )}
                                       </Popover>
                                    </MenuList>
                                 </>
                              )}
                           </Menu>
                           <input
                              type='hidden'
                              {...register('end_date', {
                                 required: 'End Date is required',
                              })}
                           />

                           {errors.end_date && (
                              <p className='err-message' role='alert'>
                                 {'  '}
                                 {errors.end_date?.message}
                              </p>
                           )}
                        </Flex>
                     </Flex>
                  </Flex>

                  <Flex direction={'column'}>
                     <Text fontSize={'14px'} fontWeight={'700'}>
                        Email recipents
                     </Text>
                     <Input
                        className='inputs'
                        placeholder='Enter recipents email'
                        aria-invalid={errors.emails ? true : false}
                        {...register('emails', {
                           required: 'Email is required',
                           pattern: {
                              value: VALID_EMAILS_PATTERN,
                              message:
                                 'Please enter valid email address/addresses',
                           },
                        })}
                     />
                     <span style={{ fontSize: 13 }}>
                        {'  '}Insert comma between recipients, if more than one
                     </span>
                     {errors.emails && (
                        <p className='err-message' role='alert'>
                           {'  '}
                           {errors.emails?.message}
                        </p>
                     )}
                  </Flex>
               </Flex>
               <Flex direction={'column'} gap={4}>
                  <Heading fontSize={'18px'} fontWeight={'500'}>
                     Metrics
                  </Heading>
                  <Input
                     className='inputs'
                     width={'30%'}
                     onChange={(e) =>
                        setfilteredKpis(
                           metaData?.filter(
                              (kpi) =>
                                 kpi.kpi_display_name
                                    .toLowerCase()
                                    .includes(e.target.value.toLowerCase()) ||
                                 (KPICategory[kpi.category] || kpi.category)
                                    .toLowerCase()
                                    .includes(e.target.value.toLowerCase()),
                           ) || [],
                        )
                     }
                     placeholder='Search metrics'
                  ></Input>
                  <Flex
                     direction={'column'}
                     gap={4}
                     border={'1px solid #EAEAEA'}
                     borderRadius={'10px'}
                     p={3}
                     className='summary'
                  >
                     <Heading
                        fontSize={'18px'}
                        fontWeight={'500'}
                        borderBottom={'1px solid #EAEAEA'}
                        pb={3}
                     >
                        Summary
                     </Heading>
                     {metaLoad ? (
                        <Stack>
                           <Skeleton height='20px' width={'30%'} />
                           <Skeleton height='20px' width={'30%'} />
                           <Skeleton height='20px' width={'30%'} />
                        </Stack>
                     ) : (
                        Object.entries(getAggData(filteredKpis))
                           .sort()
                           .map(([category, kpis], idx) => {
                              return (
                                 <Flex key={idx} direction={'column'}>
                                    <Heading
                                       display={'flex'}
                                       gap={3}
                                       alignItems={'center'}
                                       fontSize={'16px'}
                                       fontWeight={'500'}
                                       onClick={() =>
                                          setcollapseCat((prev) => {
                                             return {
                                                ...prev,
                                                [category]:
                                                   !collapseCat[category],
                                             };
                                          })
                                       }
                                    >
                                       <Checkbox
                                          isChecked={
                                             selMetrics.filter(
                                                (x) => x.category == category,
                                             ).length ==
                                             metaData?.filter(
                                                (x) => x.category == category,
                                             ).length
                                          }
                                          onChange={(e) =>
                                             handleCategoryCheck(e, category)
                                          }
                                       >
                                          {' '}
                                       </Checkbox>{' '}
                                       {'All '}{' '}
                                       {KPICategory[category] || category}{' '}
                                       {collapseCat[category] ? (
                                          <FaChevronUp className='cursor-pointer' />
                                       ) : (
                                          <FaChevronDown className='cursor-pointer' />
                                       )}
                                    </Heading>
                                    <Collapse in={!!collapseCat[category]}>
                                       <Flex
                                          flexWrap={'wrap'}
                                          gap={3}
                                          pt={4}
                                          pl={4}
                                       >
                                          {kpis
                                             .sort(
                                                (a, b) =>
                                                   a.visible_order -
                                                   b.visible_order,
                                             )
                                             .map((kpi) => (
                                                <Checkbox
                                                   key={kpi.kpi}
                                                   fontSize={'smaller'}
                                                   isChecked={
                                                      selMetrics.findIndex(
                                                         (x) =>
                                                            `${x.category}-${x.kpi}` ==
                                                            `${kpi.category}-${kpi.kpi}`,
                                                      ) > -1
                                                   }
                                                   onChange={(e) =>
                                                      handleMetricCheck(e, kpi)
                                                   }
                                                >
                                                   {kpi.kpi_display_name}
                                                </Checkbox>
                                             ))}
                                       </Flex>
                                    </Collapse>
                                 </Flex>
                              );
                           })
                     )}
                     {selKpisError && (
                        <p className='err-message' role='alert'>
                           {'  '}
                           {selKpisError}
                        </p>
                     )}
                  </Flex>

                  <Button
                     type='submit'
                     border={'1px solid #437EEB'}
                     backgroundColor={'white'}
                     _hover={{ backgroundColor: '#437EEB', color: 'white' }}
                     borderRadius={'8px'}
                     color={'#437EEB'}
                     fontWeight={'500'}
                     width={'fit-content'}
                     fontSize={'14px'}
                     disabled={mailLoading}
                     py={1}
                     px={2}
                     _disabled={{ cursor: 'not-allowed', color: 'grey' }}
                  >
                     Send now
                     {mailLoading && (
                        <Spinner
                           ml={2}
                           thickness='4px'
                           speed='0.65s'
                           emptyColor='gray.200'
                           color='blue.500'
                           size='sm'
                        />
                     )}
                  </Button>
                  <Flex justifyContent={'flex-end'} gap={3} pr={5}>
                     <Button
                        onClick={handleCancel}
                        fontSize={'14px'}
                        background={'none'}
                     >
                        Cancel
                     </Button>
                     <Button
                        onClick={handleSubmit(onSave) as () => void}
                        fontSize={'14px'}
                        _hover={{
                           scale: 1.1,
                           backgroundColor: '#437EEBEE',
                           color: 'white',
                        }}
                        backgroundColor={'#437EEB'}
                        color={'white'}
                        disabled={createMailLoading || updateMailLoading}
                     >
                        {props.report.report_id ? 'Update' : 'Save'}
                        {(createMailLoading || updateMailLoading) && (
                           <Spinner
                              ml={2}
                              thickness='4px'
                              speed='0.65s'
                              emptyColor='gray.200'
                              color='blue.500'
                              size='sm'
                           />
                        )}
                     </Button>
                  </Flex>
               </Flex>
            </Flex>
         </form>
      </Box>
   );
}

export default CreateReport;
