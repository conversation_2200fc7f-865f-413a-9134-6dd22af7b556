import React, { useState, useCallback, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { connectDisconnectToIThinkLogistics } from '../utils';
import { Keys, LocalStorageService } from '../../../utils/local-storage';
import { AuthUser } from '../../../types/auth';
import image from '../images/integrations/ithinklogistics.jpeg';
import { ithinklogisticsIntegrationSteps } from '../utils/constant';
import { NewCommerceIntegrationLayout } from './seller-panel-components/new-commerce-integration-layout';
import { IThinkLogisticsSellerPanel } from './seller-panel-components/ithinklogistics-panel';
interface FormFields {
   channelName: string;
   access_token: string;
   secret_key: string;
}

interface ApiError {
   success: boolean;
   message: string;
}

const IThinkLogisticsForm: React.FC = () => {
   const navigate = useNavigate();
   const [trying, setTrying] = useState<boolean>(false);
   const [apiError, setApiError] = useState<ApiError | null>(null);

   const client_id = LocalStorageService.getItem<AuthUser>(
      Keys.FlableUserDetails,
   )?.client_id;

   const [unmounted, setUnmounted] = useState<boolean>(false);

   const defaultState: FormFields = {
      channelName: 'ilogistics',
      access_token: '',
      secret_key: '',
   };

   const [formFields, setFormFields] = useState<FormFields>(defaultState);

   const handleChange = useCallback(
      (e: React.ChangeEvent<HTMLInputElement>) => {
         if (unmounted) return;
         const { name, value } = e.target;
         setFormFields((prev) => ({ ...prev, [name]: value }));
      },
      [unmounted],
   );

   const handleConnect = useCallback(
      (e: React.FormEvent<HTMLFormElement>) => {
         if (!client_id) return;
         e.preventDefault();
         if (unmounted) return;
         const { access_token, secret_key } = formFields;
         void (async () => {
            try {
               setTrying(true);
               setApiError({
                  success: false,
                  message: '',
               });
               await connectDisconnectToIThinkLogistics({
                  channel_name: 'ithinklogistics',
                  client_id,
                  admin_access_token: access_token,
                  channel_secret: secret_key,
                  isConnect: true,
               });
               setFormFields(defaultState);
               setApiError({
                  success: true,
                  message: 'Connection Established, Redirecting...',
               });
               setTimeout(() => {
                  navigate('/integrations');
               }, 3000);
            } catch (err) {
               let errMessage = 'Error connecting to iThink Logistics';
               if (
                  err &&
                  typeof err === 'object' &&
                  'response' in err &&
                  err.response &&
                  typeof err.response === 'object' &&
                  'data' in err.response &&
                  err.response.data &&
                  typeof err.response.data === 'object' &&
                  'message' in err.response.data
               ) {
                  errMessage =
                     (err.response.data as { message?: string }).message ||
                     errMessage;
               }
               setApiError({
                  success: false,
                  message: errMessage,
               });
            } finally {
               setTrying(false);
            }
         })();
      },
      [formFields, defaultState, unmounted],
   );

   useEffect(() => {
      setUnmounted(false);
      return () => {
         setUnmounted(true);
         setFormFields(defaultState);
         setApiError(null);
      };
   }, []);

   return (
      <NewCommerceIntegrationLayout
         title='iThink Logistics '
         description='Connect your iThink Logistics account to manage your logistics seamlessly'
         logo={image}
         logoAlt='iThink Logistics Logo'
         steps={ithinklogisticsIntegrationSteps}
      >
         <IThinkLogisticsSellerPanel
            title='Account Credentials'
            description='Please provide your Shiprocket API credentials to establish the connection'
            access_token={formFields.access_token}
            secret_key={formFields.secret_key}
            onChange={handleChange}
            onSubmit={handleConnect}
            isLoading={trying}
            apiResponse={apiError}
            submitButtonText='Connect to iThink Logistics'
            loadingText='Connecting to iThink Logistics...'
         />
      </NewCommerceIntegrationLayout>
   );
};

export default IThinkLogisticsForm;
