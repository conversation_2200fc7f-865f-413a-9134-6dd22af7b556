import { WrapI<PERSON>, VStack, Flex, Text, useColorMode } from '@chakra-ui/react';
import { useState } from 'react';
import { GrCircleQuestion } from 'react-icons/gr';
import TooltipContent from '@/pages/dashboard/components/tooltip-content';
//import { useAppSelector } from '@/store/store';
import { GoAlert } from 'react-icons/go';
import ViewAllAlertsOpp from './ViewAllAlertsOpp';
import { IoIosTrendingUp } from 'react-icons/io';
import { FaRobot } from 'react-icons/fa6';
import { Tooltip } from '@chakra-ui/react';
import './line-chart.scss';
import { useNavigate } from 'react-router-dom';
import { FaUpRightFromSquare } from 'react-icons/fa6';
import {
   setCurrentSessionID,
   setCurrentMode,
   setKpiPrompts,
   setKpiMetadata,
   clearKpiMetadata,
} from '../../../store/reducer/analytics-agent-reducer';
import { useAppSelector } from '@/store/store';
import { useAppDispatch } from '../../../store/store';
import { alertsAndOpportunities } from '@/pages/dashboard/utils/interface';
import AlertCategoryImages from '@/pages/dashboard/utils/alert-images';
const AlertsOpportunitiesCard = () => {
   const { colorMode } = useColorMode();
   const [showRightIcons, setShowRightIcons] = useState(false);
   const [hoveredIdx, setHoveredIdx] = useState<number | null>(null);
   const navigate = useNavigate();
   const dispatch = useAppDispatch();
     const { dateRange, prevRange } = useAppSelector(
      (state) => state.kpi,
   );

   const allAlertsAndOpportunities = useAppSelector(
      (state) => state.kpi.alertsAndOpportunities,
   );
   /*  if(Object.keys(allAlertsAndOpportunities).length === 0 )
    return null;*/
  const handleNavigate = (alert: alertsAndOpportunities) => {
  const { kpiName, direction, percentage } = alert;
console.log('dateRange',dateRange);
console.log('prevRange',prevRange);
  const buildAgentPrompt = () => {
    return `
You are the AI CMO analyzing ad performance.

The KPI **${kpiName}** has ${direction} by **${percentage}%** when comparing the period **${dateRange}** vs **${prevRange}**.

Please analyze:
1. What are the potential root causes for this ${direction} in ${kpiName}?
2. Which campaigns, ad sets, or audiences contributed most to the change?
3. Identify whether the shift is due to budget allocation, creative performance, targeting, or platform delivery issues.
4. Provide actionable insights and recommendations to improve ${kpiName} in the next period.
    `;
  };

  dispatch(setCurrentSessionID(''));
  dispatch(setCurrentMode('data-analyst'));
  dispatch(
    setKpiPrompts({
      displayPrompt:`Analyze alert:${ alert.message}`,  
      aiPrompt: buildAgentPrompt(),
    })
  );

  navigate('/marco/analytics-agent');
};


   return (
      <WrapItem
         minWidth={100}
         flex='1'
         background={colorMode === 'dark' ? 'gray.800' : 'white'}
         boxShadow={
            colorMode === 'dark'
               ? '1px 1px 10px 1px #00000033'
               : '1px 1px 10px 1px #cccccc33'
         }
         padding={5}
         borderRadius={5}
         position={'relative'}
         onMouseOver={() => setShowRightIcons(true)}
         onMouseLeave={() => setShowRightIcons(false)}
         className='kpi-item'
      >
         <VStack width='100%' alignItems={'flex-start'}>
            {showRightIcons && (
               <div className='right-items'>
                  <TooltipContent
                     placement='top'
                     hasArrow
                     label='Alerts & Opportunities info'
                  >
                     <GrCircleQuestion />
                  </TooltipContent>
               </div>
            )}

            <Flex width='100%' direction='column' gap={2}>
               <Text fontSize={14} fontWeight={600}>
                  Alerts & Opportunities
               </Text>

               {Object.keys(allAlertsAndOpportunities).length === 0 ? (
                  <Flex
                     flex='1'
                     width='100%'
                     align='center'
                     justify='center'
                     direction='column'
                     textAlign='center'
                     minHeight='120px'
                  >
                     <Text
                        fontSize={13}
                        fontWeight={500}
                        color={colorMode === 'dark' ? 'gray.400' : 'gray.500'}
                     >
                        No Alerts insights found for the selected date range
                     </Text>
                  </Flex>
               ) : (
                  Object.values(allAlertsAndOpportunities)
                     .slice(0, 4)
                     .map((alert, idx) => (
                        <Flex
                           key={idx}
                           align='center'
                           gap={2}
                           onMouseEnter={() => setHoveredIdx(idx)}
                           onMouseLeave={() => setHoveredIdx(null)}
                        >
                           {alert.isPositive === false ? (
                              <Flex
                                 align='center'
                                 justify='center'
                                 bg='#ffeaea'
                                 color='#e03131'
                                 borderRadius='full'
                                 boxSize='40px'
                                 pb='1px'
                              >
                                 <GoAlert size={22} />
                              </Flex>
                           ) : (
                              <Flex
                                 align='center'
                                 justify='center'
                                 bg='#e8f8ec'
                                 color='#2b8a3e'
                                 borderRadius='full'
                                 boxSize='40px'
                              >
                                 <IoIosTrendingUp size={22} />
                              </Flex>
                           )}

                           <Flex align='center' gap={2}>

                              <AlertCategoryImages
                                 category={alert.category}
                              />
                              <Text fontSize={12}>{alert.message}</Text>

                              {hoveredIdx === idx && (
                                 <Tooltip label="Analyze with AI CMO" hasArrow>
    <Flex
      align="center"
      gap={1}
      px={2}
      py={1}
      bg="blue.50"
      borderRadius="md"
      cursor="pointer"
      _hover={{ bg: "blue.100" }}
      onClick={() => handleNavigate(alert)}
    >
      <FaRobot color="#1E40AF" size={13} />
      <Text fontSize="11px" color="blue.700">Find Cause</Text>
    </Flex>
  </Tooltip>
                              )}
                           </Flex>
                        </Flex>
                     ))
               )}
            </Flex>
            <ViewAllAlertsOpp
               allAlertsAndOpportunities={allAlertsAndOpportunities}
            />
         </VStack>
      </WrapItem>
   );
};

export default AlertsOpportunitiesCard;
