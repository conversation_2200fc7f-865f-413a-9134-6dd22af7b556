/* eslint-disable @typescript-eslint/no-explicit-any */
import { PageInfo } from '../../../types/social-watch';
import endPoints from '../apis/agent';
import { channelNames } from './constant';

interface Payload {
   channel_name?: string;
   client_id?: string;
   is_active?: boolean;
   activation_date?: string;
   deactivation_date?: string;
   actual_account_name?: string;
   image_url?: string;
   channel_id: string; // Add channelId here
   channel_client_key?: string;
   channel_secret?: string;
   admin_access_token?: string;
   store_url?: string;
   deactivation_reason?: string;
   account_ids?: string;
   meta_data?: Record<
      string,
      string | number | boolean | undefined | PageInfo[]
   >;
   username?: string;
   password?: string;
   tenant_id?: string;
   facility?: string;
}

function formatDateToYYYYMMDDHHMMSS(date: Date): string {
   const year = date.getFullYear();
   const month = String(date.getMonth() + 1).padStart(2, '0');
   const day = String(date.getDate()).padStart(2, '0');
   const hours = String(date.getHours()).padStart(2, '0');
   const minutes = String(date.getMinutes()).padStart(2, '0');
   const seconds = String(date.getSeconds()).padStart(2, '0');

   return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}

function connectChannelToDB(payload: Record<string, any>) {
   return endPoints.connectChannelToDB(payload);
}
function connectUnicommerceChannelToDB(payload: Record<string, any>) {
   return endPoints.connectUnicommerceChannelToDB(payload);
}
function connectChannel(
   channel_name: string,
   client_id: string,
   isConnect: boolean = true,
   additionalData: Partial<Payload> = {},
) {
   const date = new Date();
   const payload = {
      channel_name,
      client_id,
      is_active: isConnect,
      ...additionalData,
   };

   if (isConnect) {
      payload.activation_date = formatDateToYYYYMMDDHHMMSS(date);
   } else {
      payload.deactivation_date = formatDateToYYYYMMDDHHMMSS(date);
   }
   return connectChannelToDB(payload);
}
function connectUnicommerceChannel(
   channel_name: string,
   client_id: string,
   isConnect: boolean = true,
   additionalData: Partial<Payload> = {},
) {
   const date = new Date();
   const payload = {
      channel_name,
      client_id,
      is_active: isConnect,
      ...additionalData,
   };

   if (isConnect) {
      payload.activation_date = formatDateToYYYYMMDDHHMMSS(date);
   } else {
      payload.deactivation_date = formatDateToYYYYMMDDHHMMSS(date);
   }

   return connectUnicommerceChannelToDB(payload);
}

function connectSentimentToDb(
   channel_name: string,
   client_id: string,
): Promise<any> {
   return connectChannel(channel_name, client_id);
}

function connectToYoutube({
   channel_name,
   channel_id,
   client_id,
   actual_account_name,
   image_url,
   isConnect,
}: {
   channel_name: string;
   client_id: string;
   actual_account_name?: string;
   image_url?: string;
   channel_id?: string;
   isConnect: boolean;
}) {
   return connectChannel(channel_name, client_id, isConnect, {
      actual_account_name,
      image_url,
      channel_id,
   });
}

function connectDisconnectToWoocommerce({
   channel_name,
   client_id,
   consumer_key,
   consumer_secret,
   store_url,
   isConnect = true,
}: {
   channel_name: string;
   client_id: string;
   consumer_key?: string;
   consumer_secret?: string;
   store_url?: string;
   isConnect?: boolean;
}): Promise<any> {
   return connectChannel(channel_name, client_id, isConnect, {
      channel_client_key: consumer_key,
      channel_secret: consumer_secret,
      store_url,
   });
}

function connectDisconnectToShopify({
   channel_name,
   client_id,
   channel_client_key,
   channel_secret,
   admin_access_token,
   store_url,
   access_token,
   isConnect = true,
}: {
   channel_name: string;
   client_id: string;
   channel_client_key?: string;
   channel_secret?: string;
   admin_access_token?: string;
   store_url?: string;
   isConnect?: boolean;
   access_token?: string;
}): Promise<any> {
   return connectChannel(channel_name, client_id, isConnect, {
      channel_client_key,
      channel_secret,
      store_url,
      admin_access_token,
      meta_data: { access_token },
   });
}

function connectDisconnectToShopifyConnect({
   channel_name,
   client_id,
   access_token,
   isConnect = true,
   store_url,
}: {
   channel_name: string;
   client_id: string;
   access_token?: string;
   isConnect?: boolean;
   store_url?: string;
}): Promise<any> {
   return connectChannel(channel_name, client_id, isConnect, {
      meta_data: { access_token, store_url },
   });
}
function connectDisconnectToUnicommerce({
   client_id,
   channel_name,
   username,
   password,
   tenant_id,
   facility,
   isConnect = true,
}: {
   channel_name: string;
   client_id: string;
   username?: string;
   password?: string;
   tenant_id?: string;
   facility?: string;
   isConnect?: boolean;
}): Promise<any> {
   return connectUnicommerceChannel(channel_name, client_id, isConnect, {
      username,
      password,
      tenant_id,
      facility,
   });
}

function connectDisconnectToIThinkLogistics({
   channel_name,
   client_id,
   admin_access_token,
   channel_secret,
   isConnect = true,
}: {
   channel_name: string;
   client_id: string;
   admin_access_token?: string;
   channel_secret?: string;
   isConnect?: boolean;
}): Promise<any> {
   return connectChannel(channel_name, client_id, isConnect, {
      admin_access_token,
      channel_secret,
   });
}

function connectDisconnectToShipRocket({
   channel_name,
   client_id,
   email,
   password,
   isConnect = true,
}: {
   channel_name: string;
   client_id: string;
   email?: string;
   password?: string;
   isConnect?: boolean;
}): Promise<any> {
   return connectChannel(channel_name, client_id, isConnect, {
      meta_data: { email, password },
   });
}

function connectDisconnectToVinculum({
   channel_name,
   client_id,
   api_owner,
   api_key,
   api_expiry_date,
   client_name,
   isConnect = true,
}: {
   channel_name: string;
   client_id: string;
   api_owner?: string;
   api_key?: string;
   api_expiry_date?: string;
   client_name?: string;
   isConnect?: boolean;
}): Promise<any> {
   return connectChannel(channel_name, client_id, isConnect, {
      channel_client_key: api_owner,
      channel_secret: api_key,
      actual_account_name: client_name,
      meta_data: { api_expiry_date },
   });
}
function disconnectSentimentToDb(
   channel_name: string,
   client_id: string,
): Promise<any> {
   return connectChannel(channel_name, client_id, false, {
      deactivation_reason: '',
   });
}

function connectToFbAds({
   channel_name,
   admin_access_token,
   isConnect = true,
   client_id,
   account_ids,
   source_id,
}: {
   channel_name: string;
   client_id: string;
   isConnect?: boolean;
   admin_access_token?: string;
   account_ids?: string;
   source_id?: string;
}) {
   return connectChannel(channel_name, client_id, isConnect, {
      admin_access_token,
      account_ids,
      meta_data: { source_id },
   });
}

function connectToGoogleADS({
   channel_name,
   isConnect = true,
   client_id,
   actual_account_name,
   source_id,
}: {
   channel_name: string;
   isConnect?: boolean;
   client_id: string;
   actual_account_name?: string;
   source_id?: string;
}) {
   return connectChannel(channel_name, client_id, isConnect, {
      actual_account_name,
      meta_data: { source_id },
   });
}

function connectToAspSentiment({
   channel_name,
   isConnect = true,
   client_id,
   actual_account_name,
   source_id,
   is_vendor,
}: {
   channel_name: string;
   isConnect?: boolean;
   client_id: string;
   actual_account_name?: string;
   source_id?: string;
   is_vendor?: boolean;
}) {
   return connectChannel(channel_name, client_id, isConnect, {
      actual_account_name,
      meta_data: { source_id, is_vendor },
   });
}

function connectToAmazonAdsSentiment({
   channel_name,
   isConnect = true,
   client_id,
   actual_account_name,
   source_id,
}: {
   channel_name: string;
   isConnect?: boolean;
   client_id: string;
   actual_account_name?: string;
   source_id?: string;
}) {
   return connectChannel(channel_name, client_id, isConnect, {
      actual_account_name,
      meta_data: { source_id },
   });
}

function connectToHubspotSentiment({
   channel_name,
   isConnect = true,
   client_id,
   actual_account_name,
   source_id,
}: {
   channel_name: string;
   isConnect?: boolean;
   client_id: string;
   actual_account_name?: string;
   source_id?: string;
}) {
   return connectChannel(channel_name, client_id, isConnect, {
      actual_account_name,
      meta_data: { source_id },
   });
}
function connectToZohoSentiment({
   channel_name,
   isConnect = true,
   client_id,
   actual_account_name,
   source_id,
}: {
   channel_name: string;
   isConnect?: boolean;
   client_id: string;
   actual_account_name?: string;
   source_id?: string;
}) {
   return connectChannel(channel_name, client_id, isConnect, {
      actual_account_name,
      meta_data: { source_id },
   });
}

function connectToTwitterSentiment({
   isConnect = true,
   client_id,
   actual_account_name,
   oauthToken,
   oauthTokenSecret,
   userId,
}: {
   isConnect?: boolean;
   client_id: string;
   actual_account_name?: string;
   oauthToken?: string;
   oauthTokenSecret?: string;
   userId?: string;
}) {
   return connectChannel(channelNames.TWITTER, client_id, isConnect, {
      actual_account_name,
      meta_data: { oauthToken, oauthTokenSecret, userId },
   });
}

function connectToLinkedinSentiment({
   isConnect = true,
   client_id,
   accessToken,
   refreshToken,
   pages,
}: {
   isConnect?: boolean;
   client_id: string;
   pages?: PageInfo[];
   accessToken?: string;
   refreshToken?: string;
}) {
   return connectChannel(channelNames.LINKEDIN, client_id, isConnect, {
      meta_data: { accessToken, refreshToken, pages },
   });
}

function connectToGSCSentiment({
   isConnect = true,
   client_id,
   access_token,
   refresh_token,
}: {
   isConnect?: boolean;
   client_id: string;
   access_token?: string;
   refresh_token?: string;
}) {
   return connectChannel(channelNames.GSC, client_id, isConnect, {
      meta_data: { access_token, refresh_token },
   });
}
function connectToGASentiment({
   isConnect = true,
   client_id,
   access_token,
   refresh_token,
}: {
   isConnect?: boolean;
   client_id: string;
   access_token?: string;
   refresh_token?: string;
}) {
   return connectChannel(channelNames.GOOGLE_ANALYTICS, client_id, isConnect, {
      meta_data: { access_token, refresh_token },
   });
}

function connectToMetaAdsSentiment({
   isConnect = true,
   client_id,
   access_token,
}: {
   isConnect?: boolean;
   client_id: string;
   access_token?: string;
}) {
   return connectChannel(channelNames.META_ADS, client_id, isConnect, {
      meta_data: { access_token },
   });
}

export {
   connectSentimentToDb,
   connectToYoutube,
   disconnectSentimentToDb,
   connectDisconnectToWoocommerce,
   connectDisconnectToShopify,
   connectDisconnectToShopifyConnect,
   connectDisconnectToUnicommerce,
   connectDisconnectToIThinkLogistics,
   connectToFbAds,
   connectToGoogleADS,
   connectToAspSentiment,
   connectToAmazonAdsSentiment,
   connectToTwitterSentiment,
   connectToLinkedinSentiment,
   connectToGSCSentiment,
   connectToGASentiment,
   connectToMetaAdsSentiment,
   connectToHubspotSentiment,
   connectToZohoSentiment,
   connectDisconnectToShipRocket,
   connectDisconnectToVinculum,
};
