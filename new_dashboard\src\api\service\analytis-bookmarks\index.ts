import axios, { AxiosResponse } from 'axios';
import dashboardApiAgent from '../../agent';
import { PromiseAxios } from '../common';
import Config from '@/config';

export interface FetchJobStatusPayload {
   id: string;
   user_id: string;
   client_id: string;
}

export interface FetchJobStatusResponse {
   response_status: string;
}

export const newApiClient = axios.create({
   baseURL: Config.VITE_BE_API,
   headers: {
      'Content-Type': 'application/json',
   },
   timeout: 0,
});

export const fetchJobStatus = async (
   payload: FetchJobStatusPayload,
): Promise<FetchJobStatusResponse | null> => {
   try {
      const response: AxiosResponse<FetchJobStatusResponse | null> =
         await newApiClient.get(
            `/analytics-bookmarks/${payload.client_id}/${payload.user_id}/job/${payload.id}`,
            { params: payload },
         );
      return response.data;
   } catch (err) {
      const error = err as Error;
      console.error('Error creating alert:', error.message);
      return null;
   }
};

/** MISCELLANEOUS **/
export interface Bookmark {
   id: string;
   client_id: string;
   user_id: string;
   prompt: string;
   auto_run: boolean;
   time: string;
   days: string[];
   mode: string;
   email: string;
   created_at: string;
   updated_at: string;
}

export interface FetchBookmarksPayload {
   client_id: string;
   user_id: string;
}

export interface CreateBookmarkPayload {
   prompt: string;
   auto_run: boolean;
   time: string;
   days: string[];
   mode: string;
   user_id: string;
   client_id: string;
   user_timezone: string;
   email: string;
}

export interface UpdateBookmarkPayload {
   id: string;
   prompt: string;
   auto_run: boolean;
   time: string;
   days: string[];
   mode: string;
   user_id: string;
   client_id: string;
}

export interface DeleteBookmarkPayload {
   client_id: string;
   user_id: string;
   id: string;
}

export interface StartAnalysisPayload {
   id: string;
   prompt: string;
   auto_run: boolean;
   time: string;
   days: string[];
   mode: string;
   user_id: string;
   client_id: string;
   email: string;
}

/** PAYLOADS **/

/** QUERY RESULTS **/

/** RESPONSES **/

/** ENDPOINTS **/
interface Endpoints {
   fetchBookmarks: (payload: FetchBookmarksPayload) => PromiseAxios<Bookmark[]>;

   createBookmark: (payload: CreateBookmarkPayload) => PromiseAxios<Bookmark>;

   updateBookmark: (payload: UpdateBookmarkPayload) => PromiseAxios<Bookmark>;

   deleteBookmark: (payload: DeleteBookmarkPayload) => PromiseAxios<void>;

   startAnalysis: (
      payload: StartAnalysisPayload,
   ) => PromiseAxios<{ job_id: string }>;
}

const analyticsBookmarkAPI: Endpoints = {
   fetchBookmarks: (payload: { client_id: string; user_id: string }) => {
      return dashboardApiAgent.get(
         `/analytics-bookmarks/${payload.client_id}/${payload.user_id}`,
      );
   },

   createBookmark: (payload: CreateBookmarkPayload) => {
      return dashboardApiAgent.post(
         `/analytics-bookmarks/${payload.client_id}/${payload.user_id}/create-bookmark`,
         payload,
      );
   },

   updateBookmark: (payload: UpdateBookmarkPayload) => {
      return dashboardApiAgent.put(
         `/analytics-bookmarks/${payload.client_id}/${payload.user_id}/update-bookmark/${payload.id}`,
         payload,
      );
   },

   deleteBookmark: (payload: DeleteBookmarkPayload) => {
      return dashboardApiAgent.delete(
         `/analytics-bookmarks/${payload.client_id}/${payload.user_id}/delete-bookmark/${payload.id}`,
      );
   },

   startAnalysis: (payload: StartAnalysisPayload) => {
      return dashboardApiAgent.post(
         `/analytics-bookmarks/${payload.client_id}/${payload.user_id}/analysis/${payload.id}`,
         payload,
      );
   },
};

export default analyticsBookmarkAPI;
