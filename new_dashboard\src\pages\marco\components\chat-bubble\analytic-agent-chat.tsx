import StructuredResponse from '../../utils/analytics-agent/structured-response';
import { useMemo, useRef } from 'react';
import { AnalyticsAgentChat } from '@/api/service/agentic-workflow/analytics-agent';
import { Box, Spinner } from '@chakra-ui/react';
import { Accordion, AccordionItem } from '@/components/ui/accordion';
import { AccordionContent, AccordionTrigger } from '@/components/ui/accordion';
import { useAppSelector } from '@/store/store';
import { PiClock } from 'react-icons/pi';
import { Step, StepDescription, StepIndicator } from '@chakra-ui/react';
import { StepNumber, StepSeparator, StepStatus } from '@chakra-ui/react';
import { StepTitle, Stepper } from '@chakra-ui/react';
import { formatSqlForHtml } from '../../utils/analytics-agent/helpers';

const UserChatBubble = ({ content }: { content: string }) => {
   return (
      <div className='flex justify-end w-[100%] mt-2 mb-5'>
         <div className='w-[fit-content] max-w-[85%] md:max-w-[65%] bg-[#3444AE] text-white p-[12px_14px] text-left rounded-l-[15px] rounded-r-[0px] rounded-b-[15px] text-[15px] relative inline-block'>
            <p className='text-[14px] md:text-[16px]'>{content}</p>
         </div>
      </div>
   );
};

const ChunkLoaderBubble = ({
   currentChat,
}: {
   currentChat: AnalyticsAgentChat;
}) => {
   const { chatProgress } = useAppSelector((state) => state.analyticsAgent);

   const progress = useMemo(
      () => chatProgress?.[currentChat?.chat_id],
      [chatProgress, currentChat],
   );

   return (
      <div className='flex flex-col relative'>
         <div className='flex relative mt-4'>
            {progress && progress.steps.length > 0 ? (
               <div className='font-semibold text-gray-600 text-md animate-pulse pl-1'>
                  {progress.steps[progress.steps.length - 1].step} ...
               </div>
            ) : (
               <div className='font-semibold text-gray-600 text-md animate-pulse pl-1'>
                  Thinking ...
               </div>
            )}
         </div>
         {currentChat.question_mode === 'cmo' && (
            <div className='flex flex-col items-start justify-center w-full gap-1 border-2 !border-blue-400 bg-blue-50 p-4 rounded-lg mt-4'>
               <div className='flex items-center gap-2'>
                  <PiClock size={20} color='#3C76E1' />
                  <span className='text-md text-gray-700 font-bold'>
                     Deep Analysis Initiated
                  </span>
               </div>
               <p className='text-sm text-gray-500 pl-[28px]'>
                  This complex query requires about 8-10 minutes to complete.
                  You can safely leave this page. We'll notify you when it's
                  ready.
               </p>
            </div>
         )}
      </div>
   );
};

const AgentChatBubble = ({
   currentChat,
   responseTime,
   handleSendPrompt,
   queryFetching,
}: {
   currentChat: AnalyticsAgentChat;
   responseTime: number;
   handleSendPrompt: (
      userVisiblePrompt?: string,
      actualAiPrompt?: string,
   ) => Promise<void>;
   queryFetching: boolean;
}) => {
   const { chatProgress } = useAppSelector((state) => state.analyticsAgent);
   const progress = chatProgress?.[currentChat?.chat_id];
   const thoughtRef = useRef<HTMLDivElement>(null);

   const handleAccordionToggle = () => {
      setTimeout(() => {
         thoughtRef.current?.scrollIntoView({
            behavior: 'smooth',
            block: 'start',
         });
      }, 300);
   };

   const customStyles = {
      '--indicator-size': '10px',

      '& .chakra-step': {
         display: 'grid',
         gridTemplateColumns: 'var(--indicator-size) 1fr',
         columnGap: '12px',
         alignItems: 'start',
         padding: '10px 0',
         position: 'relative',
      },

      '& .chakra-step__indicator': {
         width: 'var(--indicator-size)',
         height: 'var(--indicator-size)',
         margin: 0,
         display: 'flex',
         alignItems: 'center',
         justifyContent: 'center',
         gridColumn: 1,
         zIndex: 2,
      },

      '& .chakra-step__title': {
         gridColumn: 2,
         fontWeight: 700,
         fontSize: '14px',
      },
      '& .chakra-step__description': {
         gridColumn: 2,
         maxWidth: '800px',
      },

      '& .chakra-step__separator': {
         position: 'absolute',
         left: 'calc(var(--indicator-size) / 2)',
         transform: 'translateX(-50%)',
         bottom: 0,
         width: '2px',
         background: 'rgba(0,0,0,0.06)',
         opacity: 1,
         zIndex: 1,
      },

      '& .chakra-step:last-of-type .chakra-step__separator': {
         display: 'none',
      },
   };

   return (
      <>
         <div className='flex justify-start w-[100%] mt-2'>
            <div className='w-[100%] text-black p-[12px_14px]'>
               <div className='flex flex-col'>
                  {currentChat.response_status !== 'abandoned' &&
                     (progress ? (
                        <Accordion type='single' collapsible>
                           <AccordionItem value='item-1'>
                              <AccordionTrigger className='text-gray-500 text-[16px] justify-start gap-1 hover:cursor-pointer [&[data-state=open]>svg]:rotate-90 hover:no-underline'>
                                 <div
                                    className='text-[12px] md:text-[14px] text-gray-400'
                                    onClick={handleAccordionToggle}
                                    ref={thoughtRef}
                                 >
                                    Thought for{' '}
                                    {(responseTime / 1000).toFixed(0)}s
                                 </div>
                              </AccordionTrigger>
                              <AccordionContent className=''>
                                 <div className='flex relative'>
                                    {progress && progress.steps.length > 0 && (
                                       <Stepper
                                          index={progress.steps.length}
                                          orientation='vertical'
                                          gap='0'
                                          size='xs'
                                          colorScheme='green'
                                          style={{
                                             background: '#f5f5f5',
                                             padding: '15px 10px',
                                             borderRadius: '10px',
                                             width: '100%',
                                          }}
                                          sx={customStyles}
                                       >
                                          {progress.steps.map((step, index) => (
                                             <Step key={index}>
                                                <StepIndicator>
                                                   <StepStatus
                                                      incomplete={
                                                         <StepNumber />
                                                      }
                                                      active={<StepNumber />}
                                                   />
                                                </StepIndicator>
                                                <Box flexShrink='0'>
                                                   <StepTitle>
                                                      {step.step}
                                                   </StepTitle>
                                                   <StepDescription
                                                      dangerouslySetInnerHTML={{
                                                         __html:
                                                            step.sub_content_type ===
                                                            'sql'
                                                               ? formatSqlForHtml(
                                                                    step.sub_content,
                                                                 )
                                                               : step.sub_content,
                                                      }}
                                                   ></StepDescription>
                                                </Box>
                                                <StepSeparator />
                                             </Step>
                                          ))}
                                       </Stepper>
                                    )}
                                 </div>
                              </AccordionContent>
                           </AccordionItem>
                        </Accordion>
                     ) : (
                        <div className='text-[12px] md:text-[14px] text-gray-400 mb-4'>
                           Thought for {(responseTime / 1000).toFixed(0)}s
                        </div>
                     ))}
                  <StructuredResponse
                     query={currentChat.user_query}
                     content={currentChat.final_response}
                     currentChat={currentChat}
                     handleSendPrompt={handleSendPrompt}
                  />
               </div>
            </div>
         </div>
         {queryFetching && (
            <div className='flex flex-col items-center justify-center py-12 space-y-4 text-center'>
               <p className='text-lg font-semibold text-gray-800'>
                  Connecting to{' '}
                  <span className='text-blue-600 drop-shadow-[0_0_6px_rgba(59,130,246,0.6)]'>
                     AI&nbsp;CMO
                  </span>
               </p>
               <Spinner className='w-7 h-7 text-blue-600 animate-spin' />
            </div>
         )}
      </>
   );
};

const AnalyticAgentChat = (props: {
   currentSessionChats: AnalyticsAgentChat[];
   handleSendPrompt: (
      userVisiblePrompt?: string,
      actualAiPrompt?: string,
   ) => Promise<void>;
   queryFetching: boolean;
}) => {
   const { currentSessionChats, handleSendPrompt, queryFetching } = props;

   return (
      <>
         {currentSessionChats?.map((currentChat) => (
            <div key={currentChat.chat_id}>
               <UserChatBubble content={currentChat.user_query} />
               {currentChat.response_status === 'pending' ||
               currentChat.response_status === 'running' ? (
                  <ChunkLoaderBubble currentChat={currentChat} />
               ) : (
                  <AgentChatBubble
                     currentChat={currentChat}
                     responseTime={currentChat.response_time}
                     handleSendPrompt={handleSendPrompt}
                     queryFetching={queryFetching}
                  />
               )}
            </div>
         ))}
      </>
   );
};

export default AnalyticAgentChat;
