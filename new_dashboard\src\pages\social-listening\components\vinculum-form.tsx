import React, { useState, useCallback, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { connectDisconnectToVinculum } from '../utils';
import { Keys, LocalStorageService } from '../../../utils/local-storage';
import { AuthUser } from '../../../types/auth';
import image from '../images/integrations/Vinculum-logo-new.svg';
import { vinculumIntegrationSteps } from '../utils/constant';
import { NewCommerceIntegrationLayout } from './seller-panel-components/new-commerce-integration-layout';
import { VinculumSellerPanel } from './seller-panel-components/vinculum-panel';
interface FormFields {
   channelName: string;
   api_owner: string;
   api_key: string;
   api_expiry_date: string;
   client_name: string;
}

interface ApiError {
   success: boolean;
   message: string;
}

const VinculumForm: React.FC = () => {
   const navigate = useNavigate();
   const [trying, setTrying] = useState<boolean>(false);
   const [apiError, setApiError] = useState<ApiError | null>(null);

   const client_id = LocalStorageService.getItem<AuthUser>(
      Keys.FlableUserDetails,
   )?.client_id;

   const [unmounted, setUnmounted] = useState<boolean>(false);

   const defaultState: FormFields = {
      channelName: 'vinculum',
      api_owner: '',
      api_key: '',
      api_expiry_date: '',
      client_name: '',
   };

   const [formFields, setFormFields] = useState<FormFields>(defaultState);

   const handleChange = useCallback(
      (e: React.ChangeEvent<HTMLInputElement>) => {
         if (unmounted) return;
         const { name, value } = e.target;
         setFormFields((prev) => ({ ...prev, [name]: value }));
      },
      [unmounted],
   );

   const handleConnect = useCallback(
      (e: React.FormEvent<HTMLFormElement>) => {
         if (!client_id) return;
         e.preventDefault();
         if (unmounted) return;
         const { api_owner, api_key, api_expiry_date, client_name } =
            formFields;
         void (async () => {
            try {
               setTrying(true);
               setApiError({
                  success: false,
                  message: '',
               });
               await connectDisconnectToVinculum({
                  channel_name: 'vinculum',
                  client_id,
                  api_owner: api_owner,
                  api_key: api_key,
                  api_expiry_date: api_expiry_date,
                  client_name: client_name,
                  isConnect: true,
               });
               setFormFields(defaultState);
               setApiError({
                  success: true,
                  message: 'Connection Established, Redirecting...',
               });
               setTimeout(() => {
                  navigate('/integrations');
               }, 3000);
            } catch (err) {
               let errMessage = 'Error connecting to vinculum';
               if (
                  err &&
                  typeof err === 'object' &&
                  'response' in err &&
                  err.response &&
                  typeof err.response === 'object' &&
                  'data' in err.response &&
                  err.response.data &&
                  typeof err.response.data === 'object' &&
                  'message' in err.response.data
               ) {
                  errMessage =
                     (err.response.data as { message?: string }).message ||
                     errMessage;
               }
               setApiError({
                  success: false,
                  message: errMessage,
               });
            } finally {
               setTrying(false);
            }
         })();
      },
      [formFields, defaultState, unmounted],
   );

   useEffect(() => {
      setUnmounted(false);
      return () => {
         setUnmounted(true);
         setFormFields(defaultState);
         setApiError(null);
      };
   }, []);

   return (
      <NewCommerceIntegrationLayout
         title='Vinculum'
         description='Connect your Vinculum account to manage your logistics seamlessly'
         logo={image}
         logoAlt='Vinculum Logo'
         steps={vinculumIntegrationSteps}
      >
         <VinculumSellerPanel
            title='Account Credentials'
            description='Please provide your Vinculum API credentials to establish the connection'
            api_owner={formFields.api_owner}
            api_key={formFields.api_key}
            api_expiry_date={formFields.api_expiry_date}
            client_name={formFields.client_name}
            onChange={handleChange}
            onSubmit={handleConnect}
            isLoading={trying}
            apiResponse={apiError}
            submitButtonText='Connect to Vinculum'
            loadingText='Connecting to Vinculum...'
         />
      </NewCommerceIntegrationLayout>
   );
};

export default VinculumForm;
