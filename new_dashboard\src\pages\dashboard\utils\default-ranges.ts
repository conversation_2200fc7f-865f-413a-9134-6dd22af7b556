import {
   addDays,
   endOfDay,
   startOfDay,
   startOfMonth,
   endOfMonth,
   addMonths,
   startOfWeek,
   endOfWeek,
   isSameDay,
   startOfQuarter,
   endOfQuarter,
   startOfYear,
   endOfYear,
   addMilliseconds,
   format,
} from 'date-fns';
import { Range, StaticRange } from 'react-date-range';
import { KPIRange, Ranges, TimeRange } from './interface';
import { getPrevPeriod } from './helpers';

export const defineds = {
   startOfWeek: startOfWeek(new Date()),
   endOfWeek: endOfWeek(new Date()),
   startOfLastWeek: startOfWeek(addDays(new Date(), -7)),
   endOfLastWeek: endOfWeek(addDays(new Date(), -7)),
   startOfToday: new Date(
      format(startOfDay(new Date()), "yyyy-MM-dd'T'HH:mm:ss"),
   ),
   endOfToday: new Date(format(endOfDay(new Date()), "yyyy-MM-dd'T'HH:mm:ss")),
   startOfYesterday: new Date(
      format(startOfDay(addDays(new Date(), -1)), "yyyy-MM-dd'T'HH:mm:ss"),
   ),
   endOfYesterday: new Date(
      format(endOfDay(addDays(new Date(), -1)), "yyyy-MM-dd'T'HH:mm:ss"),
   ),
   startOfMonth: startOfMonth(new Date()),
   endOfMonth: endOfMonth(new Date()),
   startOfLastMonth: startOfMonth(addMonths(new Date(), -1)),
   endOfLastMonth: endOfMonth(addMonths(new Date(), -1)),
   startOfQuarter: startOfQuarter(new Date()),
   endOfQuarter: endOfQuarter(new Date()),
   startOfYear: startOfYear(new Date()),
   endOfYear: endOfYear(new Date()),
   startOfLast7thDay: new Date(
      format(startOfDay(addDays(new Date(), -7)), "yyyy-MM-dd'T'HH:mm:ss"),
   ),
   startOfLast30thDay: new Date(
      format(startOfDay(addDays(new Date(), -30)), "yyyy-MM-dd'T'HH:mm:ss"),
   ),
   startOfLast60thDay: new Date(
      format(startOfDay(addDays(new Date(), -60)), "yyyy-MM-dd'T'HH:mm:ss"),
   ),
   startOfLast90thDay: new Date(
      format(startOfDay(addDays(new Date(), -90)), "yyyy-MM-dd'T'HH:mm:ss"),
   ),
   startOfLast180thDay: new Date(
      format(startOfDay(addDays(new Date(), -180)), "yyyy-MM-dd'T'HH:mm:ss"),
   ),
};

const staticRangeHandler = {
   range: () => ({
      startDate: defineds.startOfToday,
      endDate: defineds.endOfToday,
   }),
   isSelected(range: Range) {
      const definedRange = this.range() as TimeRange;
      return (
         isSameDay(range.startDate || new Date(), definedRange.startDate) &&
         isSameDay(range.endDate || new Date(), definedRange.endDate)
      );
   },
};

export function createStaticRanges(ranges: Ranges[]): StaticRange[] {
   return ranges.map((range) => ({ ...staticRangeHandler, ...range }));
}

export const defaultPulseStaticRanges: StaticRange[] = createStaticRanges([
   {
      label: 'Today',
      range: () => ({
         startDate: defineds.startOfToday,
         endDate: defineds.endOfToday,
      }),
   },
   {
      label: 'Yesterday',
      range: () => ({
         startDate: defineds.startOfYesterday,
         endDate: defineds.endOfYesterday,
      }),
   },
   {
      label: 'Last 7 days',
      range: () => ({
         startDate: defineds.startOfLast7thDay,
         endDate: defineds.endOfYesterday,
      }),
   },
   {
      label: 'Last 30 days',
      range: () => ({
         startDate: defineds.startOfLast30thDay,
         endDate: defineds.endOfYesterday,
      }),
   },
   {
      label: 'Last 60 days',
      range: () => ({
         startDate: defineds.startOfLast60thDay,
         endDate: defineds.endOfYesterday,
      }),
   },
   {
      label: 'Last 90 days',
      range: () => ({
         startDate: defineds.startOfLast90thDay,
         endDate: defineds.endOfYesterday,
      }),
   },
   {
      label: 'Last Month',
      range: () => ({
         startDate: defineds.startOfLastMonth,
         endDate: defineds.endOfLastMonth,
      }),
   },
   {
      label: 'This Month',
      range: () => ({
         startDate: defineds.startOfMonth,
         endDate: defineds.endOfMonth,
      }),
   },
   {
      label: 'This Quarter',
      range: () => ({
         startDate: defineds.startOfQuarter,
         endDate: defineds.endOfQuarter,
      }),
   },
]);

export const defaultStaticRanges: StaticRange[] = createStaticRanges([
   {
      label: 'Today',
      range: () => ({
         startDate: defineds.startOfToday,
         endDate: defineds.endOfToday,
      }),
   },
   {
      label: 'Yesterday',
      range: () => ({
         startDate: defineds.startOfYesterday,
         endDate: defineds.endOfYesterday,
      }),
   },
   {
      label: 'Last 7 days',
      range: () => ({
         startDate: defineds.startOfLast7thDay,
         endDate: defineds.endOfYesterday,
      }),
   },
   {
      label: 'Last 30 days',
      range: () => ({
         startDate: defineds.startOfLast30thDay,
         endDate: defineds.endOfYesterday,
      }),
   },
   {
      label: 'Last 60 days',
      range: () => ({
         startDate: defineds.startOfLast60thDay,
         endDate: defineds.endOfYesterday,
      }),
   },
   {
      label: 'Last 90 days',
      range: () => ({
         startDate: defineds.startOfLast90thDay,
         endDate: defineds.endOfYesterday,
      }),
   },
   {
      label: 'Last 180 days',
      range: () => ({
         startDate: defineds.startOfLast180thDay,
         endDate: defineds.endOfYesterday,
      }),
   },
   {
      label: 'Last Month',
      range: () => ({
         startDate: defineds.startOfLastMonth,
         endDate: defineds.endOfLastMonth,
      }),
   },
   {
      label: 'This Month',
      range: () => ({
         startDate: defineds.startOfMonth,
         endDate: defineds.endOfMonth,
      }),
   },
   {
      label: 'This Quarter',
      range: () => ({
         startDate: defineds.startOfQuarter,
         endDate: defineds.endOfQuarter,
      }),
   },
   {
      label: 'This Year',
      range: () => ({
         startDate: defineds.startOfYear,
         endDate: defineds.endOfYear,
      }),
   },
   // {
   //   label: "This Week",
   //   range: () => ({
   //     startDate: defineds.startOfWeek,
   //     endDate: defineds.endOfWeek
   //   })
   // },
   // {
   //   label: "Last Week",
   //   range: () => ({
   //     startDate: defineds.startOfLastWeek,
   //     endDate: defineds.endOfLastWeek
   //   })
   // },
]);

export const defaultPreviousRanges = (currRange: KPIRange) =>
   createStaticRanges([
      {
         label: 'Previous Period',
         range: () => {
            const prevPeriod = getPrevPeriod(currRange);
            return {
               startDate: prevPeriod.start,
               endDate: prevPeriod.end,
            };
         },
      },
      {
         label: 'Previous week',
         range: () => ({
            startDate: addDays(currRange.start, -7),
            endDate: addMilliseconds(currRange.start, -1000),
         }),
      },
      {
         label: 'Previous month',
         range: () => ({
            startDate: addDays(currRange.start, -30),
            endDate: addMilliseconds(currRange.start, -1000),
         }),
      },
      {
         label: 'Previous quarter',
         range: () => ({
            startDate: addDays(currRange.start, -120),
            endDate: addMilliseconds(currRange.start, -1000),
         }),
      },
   ]);

// export const defaultInputRanges = [
//   {
//     label: "days up to today",
//     range(value) {
//       return {
//         startDate: addDays(
//           defineds.startOfToday,
//           (Math.max(Number(value), 1) - 1) * -1
//         ),
//         endDate: defineds.endOfToday
//       };
//     },
//     getCurrentValue(range) {
//       if (!isSameDay(range.endDate, defineds.endOfToday)) return "-";
//       if (!range.startDate) return "∞";
//       return differenceInCalendarDays(defineds.endOfToday, range.startDate) + 1;
//     }
//   },
//   {
//     label: "days starting today",
//     range(value) {
//       const today = new Date();
//       return {
//         startDate: today,
//         endDate: addDays(today, Math.max(Number(value), 1) - 1)
//       };
//     },
//     getCurrentValue(range) {
//       if (!isSameDay(range.startDate, defineds.startOfToday)) return "-";
//       if (!range.endDate) return "∞";
//       return differenceInCalendarDays(range.endDate, defineds.startOfToday) + 1;
//     }
//   }
// ];
