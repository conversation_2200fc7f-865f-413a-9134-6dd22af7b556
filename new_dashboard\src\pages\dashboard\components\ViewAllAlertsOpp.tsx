import { alertsAndOpportunities } from '../utils/interface';
import { Text } from '@chakra-ui/react';
import { useDispatch } from 'react-redux';
import { openModal } from '@/store/reducer/modal-reducer';
import { modalTypes } from '@/components/modals/modal-types';
interface ViewAllAlertsOppProps {
   allAlertsAndOpportunities: Record<string, alertsAndOpportunities>;
}
const ViewAllAlertsOpp = ({
   allAlertsAndOpportunities,
}: ViewAllAlertsOppProps) => {
   if (
      !allAlertsAndOpportunities ||
      Object.keys(allAlertsAndOpportunities).length === 0
   ) {
      return null;
   }
   console.log('allAlertsAndOpportunities===>', allAlertsAndOpportunities);
   const dispatch = useDispatch();
   //const linkColor=#7F56D9
   const handleViewOpen = () => {
      dispatch(
         openModal({
            modalType: modalTypes.ALERTS_OPPORTUNITIES_VIEW,
            modalProps: { allAlertsAndOpportunities },
         }),
      );
   };

   return (
      <Text
         cursor='pointer'
         onClick={handleViewOpen}
         fontSize={14}
         textDecoration='underline'
         mt={2}
         ml={2}
         color='#7F56D9'
         textAlign='left'
      >
         View More
      </Text>
   );
};

export default ViewAllAlertsOpp;
