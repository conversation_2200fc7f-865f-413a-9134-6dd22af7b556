import { Outlet, useLocation, useNavigate } from 'react-router-dom';
import Marco<PERSON>eader from './components/header/marco-header';
import { useAppDispatch, useAppSelector } from '@/store/store';
import { useEffect } from 'react';
import IntegrateInfo from '@/components/integrate-info/integrate-info';
import { integrationInfoStrings } from '@/utils/strings/integrate-info-strings';
import { setCurrentAgent } from '@/store/reducer/marco-reducer';

const MarcoLayout = () => {
   const dispatch = useAppDispatch();
   const navigate = useNavigate();
   const location = useLocation();

   const agentsWithHeader = [
      'analytics-agent',
      'alerting-agent',
      'meta-ads-manager-agent',
      'meta-ads-manager-auto',
   ];

   const { optimisationsStatus } = useAppSelector((state) => state.onboarding);
   const { currentAgent } = useAppSelector((state) => state.marco);

   useEffect(() => {
      if (location.pathname === '/marco') {
         navigate(`/marco/${currentAgent}`);
      } else if (
         agentsWithHeader.includes(location.pathname.split('/').pop() || '')
      ) {
         dispatch(
            setCurrentAgent(
               location.pathname.split('/').pop() as
                  | 'analytics-agent'
                  | 'meta-ads-manager-agent'
                  | 'meta-ads-manager-auto',
            ),
         );
      }
   }, [currentAgent, location.pathname, navigate]);

   if (
      !optimisationsStatus.complete &&
      !optimisationsStatus.ads_account &&
      !optimisationsStatus.flable_pixel &&
      !optimisationsStatus.channels_marketplace
   ) {
      return (
         <IntegrateInfo
            feature={integrationInfoStrings.marco.title}
            text={integrationInfoStrings.marco.description}
         />
      );
   }

   return (
      <div className='flex flex-col h-full w-full'>
         {agentsWithHeader.some((agent) =>
            location.pathname.endsWith(agent),
         ) && <MarcoHeader />}
         <div className='overflow-y-auto flex-1 w-full h-full'>
            <Outlet />
         </div>
      </div>
   );
};
export default MarcoLayout;
