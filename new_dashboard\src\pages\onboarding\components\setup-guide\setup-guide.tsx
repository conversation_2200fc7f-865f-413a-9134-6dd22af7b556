import { Box, Icon, Text } from '@chakra-ui/react';
import { useAppSelector } from '../../../../store/store';

import './setup-guide.scss';
import {
   CurrentStepIndicator,
   IncompleteStepIndicator,
   CompleteStepIndicator,
} from '../../utils/indicators';

const SetupGuide = () => {
   const { registerProgress } = useAppSelector((state) => state.onboarding);

   const renderStepIcon = (
      currentStep: number,
      registerProgress: string,
   ): React.ElementType => {
      const progress: number = Number(registerProgress.split(' ')[1]);
      if (progress === currentStep) return CurrentStepIndicator;
      if (progress > currentStep) return CompleteStepIndicator;
      return IncompleteStepIndicator;
   };

   return (
      <Box
         width='89%'
         border='1px solid #fff'
         borderRadius='8px'
         zIndex={2}
         display='flex'
         flexDirection='column'
         justifyContent='flex-start'
         alignItems='flex-start'
         padding='16px'
         color='#fff'
         className='SetupGuide'
         gap='8px'
      >
         <Box
            className='remaining-time'
            borderBottom='1px solid #fff'
            width='100%'
            pb='10px'
            fontSize='15px'
         >
            Usual time to complete:{' '}
            <span className='time' style={{ color: '#6eff8b' }}>
               2 min
            </span>
         </Box>

         <Box
            className='steps'
            borderBottom='1px solid #fff'
            width='100%'
            minH='46px'
            display='flex'
            alignItems='center'
            gap='10px'
            px='2px'
         >
            <Icon as={renderStepIcon(1, registerProgress)} boxSize={5} />
            <Text className='step-names' fontSize='16px' lineHeight='1'>
               Welcome
            </Text>
         </Box>

         <Box
            className='steps'
            width='100%'
            minH='46px'
            display='flex'
            alignItems='center'
            gap='10px'
            px='2px'
         >
            <Icon as={renderStepIcon(2, registerProgress)} boxSize={5} />
            <Text className='step-names' fontSize='16px' lineHeight='1'>
               Account Setup
            </Text>
         </Box>
      </Box>
   );
};

export default SetupGuide;
