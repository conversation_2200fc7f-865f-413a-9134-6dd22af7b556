import React, { useEffect, useState } from 'react';
import Chart from 'react-apexcharts';
import { ApexOptions } from 'apexcharts';
import { useColorMode } from '@chakra-ui/react';
import { getChartDateLabel } from '../../dashboard/utils/helpers';

interface KPIData {
   date: string;
   kpi_value: number;
}

interface ChartProp {
   kpiDetails: {
      displayName: string;
      allData: KPIData[];
      stat: string;
   };
}

const LineChartRange: React.FC<ChartProp> = ({ kpiDetails }) => {
   const chartColor =
      kpiDetails.stat.toLowerCase() === 'active' ? '#15994A' : null;

   const { colorMode } = useColorMode();

   const categories = getChartDateLabel(kpiDetails.allData);

   const [chartData, setChartData] = useState({
      options: {
         chart: {
            id: kpiDetails.displayName,
            toolbar: {
               show: true,
            },
         },
         xaxis: {
            type: 'string',
            categories: categories,
            labels: {
               show: false,
               style: {
                  colors: colorMode === 'dark' ? '#FFFFFF' : '#000000',
               },
            },
         },
         yaxis: {
            labels: {
               show: false,
               style: {
                  colors: colorMode === 'dark' ? '#FFFFFF' : '#000000',
               },
            },
         },
         stroke: {
            curve: 'smooth',
         },
         dataLabels: {
            enabled: false,
         },
         grid: {
            show: false,
         },
         colors: chartColor ? [chartColor] : undefined,
      },
      series: [
         {
            name: kpiDetails.displayName,
            data: kpiDetails.allData.map((x: KPIData) => x.kpi_value),
         },
      ],
   });

   useEffect(() => {
      setChartData({
         options: {
            chart: {
               id: kpiDetails.displayName,
               toolbar: {
                  show: false,
               },
            },
            xaxis: {
               type: 'category',
               categories: categories,
               labels: {
                  show: false, // Ensure labels are visible
                  style: {
                     colors: colorMode === 'dark' ? '#FFFFFF' : '#000000',
                  },
               },
            },
            yaxis: {
               labels: {
                  show: false, // Ensure labels are visible
                  style: {
                     colors: colorMode === 'dark' ? '#FFFFFF' : '#000000',
                  },
               },
            },
            stroke: {
               curve: 'smooth',
            },
            dataLabels: {
               enabled: false,
            },
            grid: {
               show: false,
            },
            colors: chartColor ? [chartColor] : undefined,
         },
         series: [
            {
               name: kpiDetails.displayName,
               data: kpiDetails.allData.map((x: KPIData) => x.kpi_value),
            },
         ],
      });
   }, [kpiDetails, colorMode]);

   return (
      <Chart
         options={chartData.options as ApexOptions}
         series={chartData.series}
         type='area'
         width='200'
         height='150'
      />
   );
};

export default LineChartRange;
