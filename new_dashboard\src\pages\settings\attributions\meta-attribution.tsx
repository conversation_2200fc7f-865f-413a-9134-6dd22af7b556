import React from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import {
   ChevronRightIcon,
   ArrowBackIcon,
   CopyIcon,
   CheckIcon,
} from '@chakra-ui/icons';
import { useState } from 'react';

import metaScreenshot from './meta-Img.png';

const MetaAttribution: React.FC = () => {
   const [copied2, setCopied2] = useState(false);
   const navigate = useNavigate();
   const location = useLocation();
   const state = location.state as { from?: string } | null;
   const from = state?.from;

   const goBackToSettings = () => {
      if (from === 'attributions') {
         navigate('/settings?mode=attributions');
      } else {
         navigate('/settings');
      }
   };

   const handleCopy = (
      text: string,
      setCopied: React.Dispatch<React.SetStateAction<boolean>>,
   ) => {
      navigator.clipboard.writeText(text).catch((err) => {
         console.error('Failed to copy:', err);
      });
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
   };

   return (
      <div className='w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6 overflow-auto [&::-webkit-scrollbar]:hidden [-ms-overflow-style:none] [scrollbar-width:none]'>
         <div className='flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6'>
            <div className='text-2xl font-bold text-black dark:text-white'>
               Meta Ads UTM Guide
            </div>
            <div className='flex items-center text-sm text-gray-500 dark:text-gray-400 mt-2 sm:mt-0'>
               <span
                  className='cursor-pointer hover:text-gray-700 dark:hover:text-gray-300'
                  onClick={() => navigate('/settings')}
               >
                  Settings
               </span>
               <ChevronRightIcon className='mx-1' />
               <span
                  className='cursor-pointer hover:text-gray-700 dark:hover:text-gray-300'
                  onClick={goBackToSettings}
               >
                  Attribution Agent
               </span>
               <ChevronRightIcon className='mx-1' />
               <span>Meta Ads</span>
            </div>
         </div>

         <div className='bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm overflow-hidden'>
            <div className='p-4'>
               <button
                  onClick={goBackToSettings}
                  className='flex items-center text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 transition-colors cursor-pointer'
               >
                  <ArrowBackIcon className='mr-2' />
                  <span>Back to Settings</span>
               </button>
            </div>

            <div className='px-12 py-8 max-w-5xl mx-auto'>
               <div className='mb-8 rounded-lg overflow-hidden border border-gray-200 dark:border-gray-700'>
                  <div
                     className='relative w-full'
                     style={{ maxHeight: '400px', overflow: 'hidden' }}
                  >
                     <img
                        src={metaScreenshot}
                        alt='Meta Ads Manager Screenshot'
                        className='w-full h-auto object-contain'
                        style={{ maxHeight: '400px' }}
                     />
                  </div>
               </div>

               <div className='mb-8'>
                  <h2 className='text-xl font-semibold mb-4'>
                     Open Ads Manager
                  </h2>
                  <p className='mb-2'>
                     Head over to{' '}
                     <a
                        href='https://business.facebook.com/adsmanager'
                        target='_blank'
                        rel='noopener noreferrer'
                        className='text-blue-600 dark:text-blue-400 hover:underline'
                     >
                        Meta Ads Manager
                     </a>
                     . Start creating a new campaign, or open an existing one
                     that you want to edit.
                  </p>
               </div>

               <div className='mb-8'>
                  <h2 className='text-xl font-semibold mb-4'>
                     Go to the Ad Level
                  </h2>
                  <p className='mb-2'>
                     Under the ad setup, scroll to the Destination section. In
                     the Website URL field, paste the link where you want to
                     send people.
                  </p>
               </div>

               <div className='mb-8'>
                  <h2 className='text-xl font-semibold mb-4'>
                     Paste the Ready-Made Link
                  </h2>
                  <p className='mb-2'>
                     We've already built the full UTM link for you.
                  </p>
                  <p className='mb-4'>
                     Simply copy the link below and paste it directly into
                     Meta's URL Parameters field (like in the image shown). This
                     skips the manual steps entirely.
                  </p>
                  <div className='bg-gray-100 dark:bg-gray-700 p-3 rounded-md flex justify-between items-center mb-2'>
                     <code className='text-sm break-all'>
                        utm_source=facebook&utm_medium=paid&utm_campaign=&#123;&#123;campaign.name&#125;&#125;&utm_content=&#123;&#123;ad.name&#125;&#125;&utm_term=&#123;&#123;adset.name&#125;&#125;&placement=&#123;&#123;placement&#125;&#125;&ad_id=&#123;&#123;ad.id&#125;&#125;
                     </code>
                     <button
                        onClick={() =>
                           handleCopy(
                              'utm_source=facebook&utm_medium=paid&utm_campaign={{campaign.name}}&utm_content={{ad.name}}&utm_term={{adset.name}}&placement={{placement}}&ad_id={{ad.id}}',
                              setCopied2,
                           )
                        }
                        className='text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 ml-2 flex-shrink-0'
                     >
                        {copied2 ? <CheckIcon /> : <CopyIcon />}
                     </button>
                  </div>
               </div>

               <div className='mb-4'>
                  <p className='font-medium'>That's it—you're good to go! 🚀</p>
               </div>
            </div>
         </div>
      </div>
   );
};

export default MetaAttribution;
