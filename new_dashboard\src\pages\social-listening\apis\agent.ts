/* eslint-disable @typescript-eslint/no-explicit-any */
import axios, { AxiosInstance, AxiosResponse } from 'axios';
import Config from '../../../config';

const agent: AxiosInstance = axios.create({
   baseURL: Config.VITE_SOCIAL_LISTENING_API,
   withCredentials: true,
});
interface TwitterUserPayload {
   client_id: string;
   user_id: string;
}
interface AccessTokenPayload {
   oauth_token: string;
   oauth_verifier: string;
}
type Payload = Record<string, string | number>;

interface CreateSourcePayload {
   workspaceId: string;
   accessToken?: string;
   accountIds?: string[];
   dateRange?: string;
   sourceType: string;
   name: string;
   secretId?: string;
}

interface CreateDestinationPayload {
   name: string;
   workspaceId: string;
   schema: string;
}

interface CreateConnectionPayload {
   name: string;
   sourceId: string;
   destinationId: string;
   prefix: string;
}

interface Params {
   [key: string]: string;
}

interface TriggerSyncPayload {
   connectionId: string;
}

const newDashboardAgent: AxiosInstance = axios.create({
   baseURL: Config.VITE_BE_API,
   withCredentials: true,
});

interface YoutubeInfoData {
   youtubeData: {
      totalResults: number;
      title: string;
      url: string;
   };
   success: boolean;
}

type PromiseAxios<T> = Promise<AxiosResponse<T>>;

export interface SentimentConnectionDetails {
   details: {
      actual_account_name: string;
      image_url: string;
      is_active: boolean;
      store: string;
      channel_id: string;
      meta_data: Record<string, string>;
   } | null;
}
export interface GAProperty {
   propertyId: string; // propertyId
   parent: string;
   createTime: string;
   updateTime: string;
   displayName: string;
   currencyCode: string;
   timeZone: string;
   account: string;
   serviceLevel: string;
   propertyType: string;
}

export interface GAAccountWithProperties {
   accountId: string;
   displayName: string;
   regionCode: string;
   createTime: string;
   updateTime: string;
   properties: GAProperty[];
}
export interface metaAdsAccounts {
   name: string;
   account_id: string;
   account_status: number;
   id: string;
}
export interface UnicommerceConnectionDetails {
   is_active: boolean;
}

export interface FivetranSourceOut {
   id: string | null;
   ok: boolean;
   msg: string;
}

interface Endpoints {
   getYoutubeChannelInfo: (
      channelId: string,
   ) => Promise<AxiosResponse<YoutubeInfoData>>;
   connectWithYoutube: (payload: {
      channelId: string;
      client_id: string;
   }) => Promise<AxiosResponse<{ message: string }>>;
   disconnectYoutube: (payload: {
      channel_id: string;
      client_id: string;
   }) => Promise<AxiosResponse<{ message: string }>>;
   getChannelReviews: (params: Params) => Promise<any>;
   getChannelChartData: (params: Params) => Promise<any>;
   getChannelTopicReportSummary: (params: Params) => Promise<any>;
   getChannelTopicDetails: (params: Params) => Promise<any>;
   connectChannelToDB: (
      payload: Payload,
   ) => Promise<AxiosResponse<{ message: string }>>;
   connectUnicommerceChannelToDB: (
      payload: Payload,
   ) => Promise<AxiosResponse<{ message: string }>>;
   checkConnectionDetails: (payload: {
      client_id: string;
      channel_name: string;
   }) => Promise<AxiosResponse<SentimentConnectionDetails>>;
   checkUnicommerceConnectionDetails: (payload: {
      client_id: string;
      channel_name: string;
   }) => Promise<AxiosResponse<UnicommerceConnectionDetails>>;
   getRequestToken: () => Promise<any>;
   getAccessToken: (payload: AccessTokenPayload) => Promise<any>;
   getUserProfile: () => Promise<any>;
   fetchUserTweets: (userId: string, payload: Payload) => Promise<any>;
   disconnectTwitter: (payload: Payload) => Promise<any>;
   createTwitterUser: (payload: TwitterUserPayload) => Promise<any>;
   createSource: (
      payload: CreateSourcePayload,
   ) => PromiseAxios<{ sourceId: string }>;

   deleteAirbyteSource: (sourceId: string) => PromiseAxios<void>;

   createDestination: (
      payload: CreateDestinationPayload,
   ) => PromiseAxios<{ destinationId: string }>;

   createConnection: (
      payload: CreateConnectionPayload,
   ) => PromiseAxios<{ connectionId: string }>;

   triggerSync: (
      payload: TriggerSyncPayload,
   ) => PromiseAxios<{ jobId: string }>;

   redirectToGoogle: () => PromiseAxios<{ url: string }>;

   initiateSource: (payload: {
      sourceType: string;
   }) => PromiseAxios<{ authData: { consentUrl: string } }>;

   createSourceFivetran: (payload: {
      clientId: string;
      sourceType: string;
      metaData?: { isVendor?: boolean; zohoRegion?: string };
   }) => PromiseAxios<{ sourceDetails: FivetranSourceOut }>;

   createConnectCardFivetran: (payload: {
      sourceId: string;
      redirectPathName: string;
   }) => PromiseAxios<{ uri: string | null }>;

   deleteConnector: (payload: {
      sourceId: string;
   }) => PromiseAxios<{ ok: boolean }>;
   // temp meta_ads
   getMetaAdsAuthUrl: () => PromiseAxios<{ url: string }>;
   getMetaAdsAccounts: (payload: {
      userAccessToken: string;
   }) => PromiseAxios<metaAdsAccounts[]>;
   fetchMetaCredentials: (payload: {
      client_id: string;
   }) => PromiseAxios<{ access_token: string }>;
   saveMetaAdsSelections: (payload: {
      clientId: string;
      selectedAccounts: string[];
   }) => PromiseAxios<{
      success: boolean;
      message: string;
      savedAccounts?: string[];
   }>;
   getAuthUrl: () => PromiseAxios<{ url: string }>;
   getGaAuthUrl: () => PromiseAxios<{ url: string }>;
   fetchGAAccountsAndProperties: (payload: {
      clientId: string;
   }) => Promise<AxiosResponse<GAAccountWithProperties[]>>;
   // GAAccountWithProperties[]>;
   saveGASelections: (payload: {
      clientId: string;
      selectedProperties: {
         account_id: string;
         property_ids: string[];
      }[];
   }) => Promise<AxiosResponse<{ message: string }>>;
   installShopifyApp: (shop: string) => Promise<AxiosResponse<{ url: string }>>;
   getShopifyInstallUrl: () => Promise<AxiosResponse<{ url: string }>>;
   checkWhatsappConnectionDetails: (payload: {
      client_id: string;
   }) => Promise<{ is_active?: boolean }>;
   connectWhatsapp: (payload: {
      client_id: string;
      phone_number: string;
   }) => Promise<{ success: boolean; message: string }>;
   verifyWhatsapp: (payload: {
      client_id: string;
      phone_number: string;
      otp: string;
   }) => Promise<{ success: boolean; message: string }>;
   disconnectWhatsapp: (payload: {
      client_id: string;
   }) => Promise<{ success: boolean; message: string }>;
   validateShipRocketAcc: (payload: {
      email: string;
      password: string;
   }) => Promise<AxiosResponse<{ valid: boolean; message: string }>>;
   encryptCreds: (payload: {
      password: string;
   }) => Promise<AxiosResponse<string>>;
}

const endPoints: Endpoints = {
   getYoutubeChannelInfo: (channelId) =>
      agent.get(`/youtube/channel/${channelId}`),
   connectWithYoutube: (payload) =>
      agent.post(`/youtube/channel/${payload.channelId}`, payload),
   disconnectYoutube: (payload) => agent.post('/youtube/disconnect', payload),
   getChannelReviews: (params) => agent.get('/sentiment', { params }),
   getChannelChartData: (params) =>
      agent.get('/sentiment/topic-dashboard', { params }),
   getChannelTopicReportSummary: (params) =>
      agent.get('/sentiment/topic-report-summary', { params }),
   getChannelTopicDetails: (params) =>
      agent.get('/sentiment/topic-details', { params }),
   connectChannelToDB: (payload) =>
      agent.post('/sentiment/connect-channel', payload),
   checkConnectionDetails: (payload) =>
      agent.post('/sentiment/check-connection', payload),
   checkUnicommerceConnectionDetails: (payload) =>
      agent.post('/sentiment/check-connection/unicommerce', payload),
   connectUnicommerceChannelToDB: (payload) =>
      agent.post('/sentiment/connect-channel/unicommerce', payload),
   // twitter
   getRequestToken: () => agent.post('/twitter/auth/request_token', {}),
   getAccessToken: (payload) =>
      agent.post('/twitter/auth/access_token', payload),
   getUserProfile: () => agent.get('/twitter/auth/profile'),
   fetchUserTweets: (userId, payload) =>
      agent.post(`/twitter/users/${userId}/tweets`, payload),
   disconnectTwitter: (payload) =>
      agent.post('/twitter/auth/disconnect', payload),
   createTwitterUser: (payload) =>
      agent.post('/twitter/auth/create-twitter-user', payload),
   createSource: (payload) =>
      newDashboardAgent.post('/airbyte/source', payload),
   deleteAirbyteSource: (sourceId) =>
      newDashboardAgent.post('/airbyte/remove', { sourceId }),

   createConnection: (payload) =>
      newDashboardAgent.post('/airbyte/connection', payload),
   createDestination: (payload) =>
      newDashboardAgent.post('/airbyte/destination', payload),

   triggerSync: (payload) => newDashboardAgent.post('/airbyte/job', payload),

   // google ads
   redirectToGoogle: () => agent.post('/google-ads/login', {}),

   initiateSource: (payload) =>
      newDashboardAgent.post('/airbyte/auth', payload),

   createSourceFivetran: (payload) =>
      newDashboardAgent.post('/fivetran/source', payload),
   createConnectCardFivetran: (payload) =>
      newDashboardAgent.post('/fivetran/connect-card', payload),

   deleteConnector: (payload) =>
      newDashboardAgent.post('/fivetran/remove', payload),
   //temp meta_ads
   getMetaAdsAuthUrl: () => newDashboardAgent.get('/meta-ads/login'),
   getMetaAdsAccounts: (payload) =>
      newDashboardAgent.post('/meta-ads/accounts', payload),
   fetchMetaCredentials: (payload) =>
      newDashboardAgent.post(`/meta-ads/creds`, payload),
   saveMetaAdsSelections: (payload) =>
      newDashboardAgent.post('/meta-ads/save/selected-accounts', payload),
   getAuthUrl: () => newDashboardAgent.get('/google-gsc/login'),
   getGaAuthUrl: () => newDashboardAgent.get('/google-analytics/login'),
   fetchGAAccountsAndProperties: (payload) =>
      newDashboardAgent.post('/google-analytics/all-properties', payload),
   saveGASelections: (payload) =>
      newDashboardAgent.post(
         '/google-analytics/save/selected-properties',
         payload,
      ),
   installShopifyApp: (shop) =>
      newDashboardAgent.get(`/shopify/install?shop=${shop}`),
   getShopifyInstallUrl: () => newDashboardAgent.get(`/shopify/install-url`),
   checkWhatsappConnectionDetails: async ({ client_id }) => {
      const res = await agent.post<{ is_active?: boolean }>(
         '/whatsapp/connection-details',
         { client_id },
      );
      return res.data;
   },
   connectWhatsapp: async ({ client_id, phone_number }) => {
      const res = await agent.post<{ success: boolean; message: string }>(
         '/whatsapp/connect/',
         { client_id, phone_number },
      );
      return res.data;
   },
   verifyWhatsapp: async ({ client_id, phone_number, otp }) => {
      const res = await agent.post<{ success: boolean; message: string }>(
         '/whatsapp/verify/',
         { client_id, phone_number, otp },
      );
      return res.data;
   },
   disconnectWhatsapp: async ({ client_id }) => {
      const res = await agent.post<{ success: boolean; message: string }>(
         '/whatsapp/disconnect',
         { client_id },
      );
      return res.data;
   },
   validateShipRocketAcc: async ({ email, password }) => {
      return newDashboardAgent.post<{ valid: boolean; message: string }>(
         '/shiprocket/validate',
         { email, password },
      );
   },
   encryptCreds: async ({ password }) => {
      return newDashboardAgent.post<string>('/shiprocket/encrypt', {
         password,
      });
   },
};

export default endPoints;
