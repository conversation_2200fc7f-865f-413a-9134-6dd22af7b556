import image from '../images/integrations/googleAnalytics.png';
import Card from './Card';
import endPoints from '../apis/agent';
import LoaderModal from '../../../components/modals/LoaderModal';
import { useEffect, useState } from 'react';
import { useApiMutation, useApiQuery } from '../../../hooks/react-query-hooks';
import { channelNames } from '../utils/constant';
import { Keys, LocalStorageService } from '../../../utils/local-storage';
import { AuthUser } from '../../../types/auth';
import { connectToGASentiment } from '../utils';
import { dialogMessage } from '../../../utils/strings/content-manager';
import { ApiError } from './facebook-ads-form';
import { useToast } from '@chakra-ui/react';
import { GAAccountWithProperties } from '../apis/agent';
import Swal from 'sweetalert2';
import {
   showAccountSelectModal,
   showMultiSelectModal,
   showConfirmationModal,
} from '../utils/modal-helpers';

const GoogleAnalytics = () => {
   const [isDisconnecting, setIsDisconnecting] = useState(false);
   const toast = useToast();
   const authUser = LocalStorageService.getItem<AuthUser>(
      Keys.FlableUserDetails,
   );

   const showToast = (
      title: string,
      description: string,
      status: 'success' | 'error',
   ) => {
      toast({ title, description, status, duration: 2000, isClosable: true });
   };

   const { data, isLoading, errorMessage } = useApiQuery({
      queryKey: [`gaConnectionDetails`],
      queryFn: () =>
         endPoints.checkConnectionDetails({
            client_id: authUser?.client_id || '',
            channel_name: channelNames.GOOGLE_ANALYTICS,
         }),
   });

   const {
      mutateAsync: fetchGAAccounts,
      isPending: isFetchingGA,
      //data: gaResponse,
      //errorMessage: gaErrorMessage,
   } = useApiMutation<GAAccountWithProperties[], { clientId: string }>({
      mutationFn: endPoints.fetchGAAccountsAndProperties,
   });

   const {
      mutateAsync: saveSelectedGAProperties,
      // isPending: isSavingGAProperties,
      //data: gaResponse,
      //errorMessage: gaErrorMessage,
   } = useApiMutation({
      mutationFn: endPoints.saveGASelections,
   });

   const { mutate: connectToSentiment, isPending: isConnectingToSentiment } =
      useApiMutation({
         mutationFn: connectToGASentiment,

         onSuccessHandler: async (_data, payload) => {
            if (!payload?.isConnect) {
               setTimeout(() => {
                  window.location.href = `${window.location.origin}/integrations`;
               }, 1000);
               return;
            }
            try {
               if (!authUser?.client_id) {
                  showToast('Error', 'Client ID missing', 'error');
                  return;
               }

               const gaAccounts: GAAccountWithProperties[] =
                  await fetchGAAccounts({
                     clientId: authUser.client_id,
                  });

               if (!gaAccounts.length) {
                  const confirmed = await showConfirmationModal(
                     dialogMessage.noGaAccount.title,
                     dialogMessage.noGaAccount.description,
                     {
                        icon: 'warning',
                        confirmButtonText: 'OK',
                        showCancelButton: false,
                     },
                  );
                  if (confirmed) {
                     connectToSentiment({
                        client_id: authUser?.client_id,
                        isConnect: false,
                     });
                  }
                  return;
               }
               const accountOptions: Record<string, string> = {};
               gaAccounts.forEach((account) => {
                  accountOptions[account.accountId] =
                     `${account.displayName}, ${account.accountId}`;
               });

               const selectedAccountId =
                  await showAccountSelectModal(accountOptions);
               if (!selectedAccountId) {
                  connectToSentiment({
                     client_id: authUser?.client_id,
                     isConnect: false,
                  });
                  return;
               }
               const selectedAccount = gaAccounts.find(
                  (acc): acc is GAAccountWithProperties =>
                     acc.accountId === selectedAccountId,
               );

               if (!selectedAccount) {
                  connectToSentiment({
                     client_id: authUser?.client_id,
                     isConnect: false,
                  });
                  showToast('Error', 'Account not found', 'error');
                  return;
               }
               const propertyOptions: Record<string, string> = {};
               selectedAccount.properties.forEach((prop) => {
                  if (prop.propertyId && prop.displayName) {
                     propertyOptions[prop.propertyId] =
                        `${prop.displayName} [${prop.propertyId}]`;
                  }
               });

               const selectedPropertyIds = await showMultiSelectModal(
                  'Select GA Properties',
                  propertyOptions,
               );
               if (!selectedPropertyIds) {
                  connectToSentiment({
                     client_id: authUser?.client_id,
                     isConnect: false,
                  });
                  return;
               }

               if (selectedPropertyIds && selectedAccount) {
                  await saveSelectedGAProperties({
                     clientId: authUser?.client_id,
                     selectedProperties: [
                        {
                           account_id: selectedAccount.accountId,
                           property_ids: selectedPropertyIds,
                        },
                     ],
                  });
               }
               showToast('Success', 'Connected to Google Analytics', 'success');

               setTimeout(() => {
                  window.location.href = `${window.location.origin}/integrations`;
               }, 1000);
            } catch (error) {
               showToast(
                  'Warning',
                  'Connected, but failed to fetch GA properties',
                  'error',
               );
               console.error('Failed to fetch GA properties:', error);
            }
         },
      });

   const { is_active = false } = data?.details || {};

   async function onConnect() {
      const {
         data: { url },
      } = await endPoints.getGaAuthUrl();
      window.location.href = url;
   }

   const onDisconnect = async () => {
      const result = await Swal.fire({
         title: dialogMessage.delete.title,
         text: dialogMessage.delete.description,
         icon: 'warning',
         showCancelButton: true,
         confirmButtonColor: '#3085d6',
         cancelButtonColor: '#d33',
         confirmButtonText: dialogMessage.delete.buttonMessage,
      });
      if (result.isConfirmed && authUser?.client_id) {
         try {
            setIsDisconnecting(true);
            connectToSentiment({
               client_id: authUser?.client_id,
               isConnect: false,
            });
         } catch (err) {
            const error = err as ApiError;
            const msg = error.response.data.message;
            showToast('Could not disconnect', msg!, 'error');
         } finally {
            setIsDisconnecting(false);
         }
      }
   };

   function onClick() {
      is_active ? void onDisconnect() : void onConnect();
   }

   useEffect(() => {
      const searchParams = new URLSearchParams(window.location.search);
      const ga = searchParams.get('ga');
      const accessToken = searchParams.get('t');
      const refreshToken = searchParams.get('r');

      if (ga && accessToken && refreshToken && authUser?.client_id) {
         connectToSentiment({
            client_id: authUser.client_id,
            isConnect: true,
            access_token: accessToken,
            refresh_token: refreshToken,
         });
      }
   }, []);

   return (
      <>
         {isFetchingGA && <LoaderModal />}

         <Card
            error={errorMessage}
            isConnected={is_active}
            isDisconnecting={isDisconnecting}
            isConnecting={isConnectingToSentiment}
            isFetching={isLoading}
            onButtonClick={onClick}
            heading='Google Analytics'
            src={image}
         />
      </>
   );
};

export default GoogleAnalytics;
