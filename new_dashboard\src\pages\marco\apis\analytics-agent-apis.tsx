import { AuthUser } from '@/types/auth';
import { useAppSelector } from '@/store/store';
import { useApiMutation, useApiQuery } from '@/hooks/react-query-hooks';
import { Keys, LocalStorageService } from '@/utils/local-storage';
import analyticAgentAPI, {
   AnalyticsAgentChat,
} from '../../../api/service/agentic-workflow/analytics-agent';
import analyticsAgentAPI from '../../../api/service/agentic-workflow/analytics-agent';
import analyticsBookmarkAPI, {
   Bookmark,
} from '@/api/service/analytis-bookmarks';

export const useFetchChatHistoryQuery = () => {
   const { currentSessionID } = useAppSelector((state) => state.analyticsAgent);

   const { client_id, user_id } =
      LocalStorageService.getItem<AuthUser>(Keys.FlableUserDetails) ?? {};

   const fetchSessionHistoryPayload = {
      client_id: client_id || '',
      session_id: currentSessionID || '',
      user_id: user_id || '',
   };

   return useApiQuery({
      queryKey: ['sessionHistory', currentSessionID],
      queryFn: () =>
         analyticsAgentAPI.fetchSessionHistory(fetchSessionHistoryPayload),
      enabled: !!currentSessionID,
      refetchOnWindowFocus: false,
      refetchOnReconnect: false,
      refetchOnMount: false,
   });
};

export const useFetchHistoryQuery = () => {
   const { client_id, user_id } =
      LocalStorageService.getItem<AuthUser>(Keys.FlableUserDetails) || {};

   const { currentAgent } = useAppSelector((state) => state.marco);
   const { currentPage } = useAppSelector((state) => state.analyticsAgent);

   return useApiQuery<AnalyticsAgentChat[]>({
      queryKey: ['analytics-chat-history', currentAgent, String(currentPage)],
      queryFn: async () =>
         analyticAgentAPI.fetchAllSessionsHistory({
            client_id: client_id || '',
            user_id: user_id || '',
            page: currentPage,
         }),
      enabled: !!client_id && !!user_id && currentAgent === 'analytics-agent',
      refetchOnMount: false,
      refetchOnWindowFocus: false,
      refetchOnReconnect: false,
   });
};

export const useFetchFeatureUsageQuery = () => {
   const { client_id, user_id } =
      LocalStorageService.getItem<AuthUser>(Keys.FlableUserDetails) ?? {};

   const fetchFeatureUsagePayload = {
      client_id: client_id || '',
      user_id: user_id || '',
      feature_name: 'analytics_agent',
      feature_type: 'agent',
   };

   return useApiQuery({
      queryKey: ['featureUsage', 'analytics-agent'],
      queryFn: () =>
         analyticsAgentAPI.fetchUserFeatureUsage(fetchFeatureUsagePayload),
      enabled: !!client_id && !!user_id,
      refetchOnWindowFocus: false,
      refetchOnReconnect: false,
      refetchOnMount: false,
   });
};

export const useTrackFeatureUsageMutation = () => {
   return useApiMutation({
      queryKey: ['trackFeatureUsage', 'analytics-agent'],
      mutationFn: analyticsAgentAPI.trackFeatureUsage,
   });
};

export const useLikeDislikeChatMutation = () => {
   return useApiMutation({
      queryKey: ['likeDislikeChat'],
      mutationFn: analyticsAgentAPI.likeDislikeChat,
   });
};

export const useUpdateChatRewrittenMutation = () => {
   return useApiMutation({
      queryKey: ['updateChatRewritten'],
      mutationFn: analyticsAgentAPI.updateChatRewritten,
   });
};

export const useUpdateChatCopiedMutation = () => {
   return useApiMutation({
      queryKey: ['updateChatCopied'],
      mutationFn: analyticsAgentAPI.updateChatCopied,
   });
};

export const useUpdateChatFeedbackMutation = () => {
   return useApiMutation({
      queryKey: ['updateChatFeedback'],
      mutationFn: analyticsAgentAPI.updateChatFeedback,
   });
};

// BOOKMARKS
export const useFetchBookmarksQuery = () => {
   const { client_id, user_id } =
      LocalStorageService.getItem<AuthUser>(Keys.FlableUserDetails) || {};

   return useApiQuery<Bookmark[]>({
      queryKey: ['bookmarks', String(client_id), String(user_id)],
      queryFn: async () =>
         analyticsBookmarkAPI.fetchBookmarks({
            client_id: String(client_id) || '',
            user_id: String(user_id) || '',
         }),
      enabled: !!client_id && !!user_id,
      refetchOnMount: false,
      refetchOnWindowFocus: false,
      refetchOnReconnect: false,
   });
};

export const useCreateBookmarkMutation = () => {
   return useApiMutation({
      queryKey: ['createBookmark'],
      mutationFn: analyticsBookmarkAPI.createBookmark,
   });
};

export const useUpdateBookmarkMutation = () => {
   return useApiMutation({
      queryKey: ['updateBookmark'],
      mutationFn: analyticsBookmarkAPI.updateBookmark,
   });
};

export const useDeleteBookmarkMutation = () => {
   return useApiMutation({
      queryKey: ['deleteBookmark'],
      mutationFn: analyticsBookmarkAPI.deleteBookmark,
   });
};

export const useStartAnalysisMutation = () => {
   return useApiMutation({
      queryKey: ['startAnalysis'],
      mutationFn: analyticsBookmarkAPI.startAnalysis,
   });
};
